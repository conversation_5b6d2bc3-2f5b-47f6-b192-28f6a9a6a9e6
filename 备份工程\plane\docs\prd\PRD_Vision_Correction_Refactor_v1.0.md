# 视角校正功能重构产品需求文档 (PRD)

**版权信息：** 米醋电子工作室  
**文档版本：** v1.0  
**创建日期：** 2025-01-30  
**产品经理：** Emma  
**编码格式：** UTF-8

## 1. 文档信息

### 1.1 版本历史
| 版本 | 日期 | 修改内容 | 负责人 |
|------|------|----------|--------|
| v1.0 | 2025-01-30 | 初始版本，视角校正功能重构需求 | Emma |

### 1.2 负责人信息
- **产品经理：** Emma
- **架构师：** Bob  
- **工程师：** Alex
- **数据分析师：** David

## 2. 背景与问题陈述

### 2.1 问题背景
在当前的野生动物巡查系统中，视角校正功能存在严重的失效问题。该功能原本设计用于在检测到动物后自动调整无人机位置，使动物居中显示以提高识别精度。

### 2.2 核心问题
1. **位置覆盖问题**：视角校正逻辑被深度嵌套在case 4巡逻状态机内部，其更新的`target_pos`数组会被外层的`set_target_position()`调用覆盖
2. **时序冲突**：每次循环开始时，`set_target_position()`都会用固定的巡逻路径坐标重置目标位置，导致视角校正的位置更新失效
3. **架构问题**：视角校正逻辑缺乏独立的执行空间，无法获得足够的优先级

### 2.3 影响分析
- **功能失效**：视角校正功能完全无法工作，影响动物识别精度
- **用户体验**：动物检测结果不准确，影响巡查任务的有效性
- **系统稳定性**：复杂的嵌套结构增加了代码维护难度和出错概率

## 3. 目标与成功指标

### 3.1 项目目标 (Objectives)
1. **功能恢复**：完全解决视角校正功能失效问题
2. **架构优化**：简化代码嵌套结构，提高可维护性
3. **性能提升**：确保三个功能模块（巡逻、动物检测、视角校正）协调工作

### 3.2 关键结果 (Key Results)
1. **KR1**：视角校正功能成功率达到100%，不再被巡逻逻辑覆盖
2. **KR2**：代码嵌套层级从4层减少到2层，提高可读性50%
3. **KR3**：编译通过率100%，无功能回归问题
4. **KR4**：状态机响应时间<20ms，满足实时性要求

### 3.3 反向指标 (Counter Metrics)
1. **代码复杂度**：不增加新的全局变量或状态枚举
2. **向后兼容性**：现有函数接口保持100%不变
3. **性能开销**：内存增加<4字节，CPU开销可忽略

## 4. 用户画像与用户故事

### 4.1 目标用户
- **主要用户**：野生动物巡查系统操作员
- **次要用户**：系统维护工程师、代码开发人员

### 4.2 用户故事
**作为** 野生动物巡查系统操作员  
**我希望** 当无人机检测到动物时，能够自动调整位置使动物居中显示  
**以便于** 获得更准确的动物识别结果和数量统计

**作为** 系统维护工程师  
**我希望** 代码结构清晰简洁，嵌套层级合理  
**以便于** 快速定位问题和进行功能维护

## 5. 功能规格详述

### 5.1 核心功能设计

#### 5.1.1 状态机层级重构
- **新增case 3状态**：专门处理视角校正逻辑
- **状态优先级**：case 3优先级高于case 4巡逻状态
- **状态跳转**：case 3 ↔ case 4 的双向切换机制

#### 5.1.2 视角校正逻辑提取
- **函数封装**：将视角校正等待逻辑从`process_animal_detection()`中提取
- **独立管理**：创建专门的视角校正状态处理函数
- **接口保持**：确保原函数接口完全不变

#### 5.1.3 位置设置优化
- **冲突避免**：视角校正期间暂停巡逻逻辑的位置设置
- **状态隔离**：通过状态机确保位置更新的独占性
- **恢复机制**：视角校正完成后自动恢复巡逻状态

### 5.2 技术实现规范

#### 5.2.1 状态机设计
```c
case 3: // 视角校正优先处理状态
    if (auto_pos_state == AUTO_POS_MOVING) {
        // 等待视角校正完成
        if (is_position_reached()) {
            auto_pos_state = AUTO_POS_COMPLETED;
            mission_step = 4; // 返回巡逻状态
        }
        return; // 视角校正期间暂停其他操作
    }
    mission_step = 4; // 无视角校正需求，直接进入巡逻
    break;
```

#### 5.2.2 代码风格要求
- **编码格式**：UTF-8编码，中文注释
- **命名规范**：遵循现有的下划线命名风格
- **调试输出**：使用`AnoPTv8SendStr()`进行状态跟踪
- **错误处理**：完整的异常情况处理机制

### 5.3 边缘情况与异常处理

#### 5.3.1 状态异常处理
- **状态超时**：视角校正超时自动恢复到巡逻状态
- **位置异常**：无效位置数据时的安全处理
- **通信中断**：传感器数据异常时的降级处理

#### 5.3.2 兼容性处理
- **版本兼容**：与现有代码库完全兼容
- **接口兼容**：所有外部调用接口保持不变
- **数据兼容**：现有数据结构和格式不变

## 6. 范围定义

### 6.1 包含功能 (In Scope)
1. ✅ **状态机重构**：添加case 3视角校正状态
2. ✅ **逻辑提取**：视角校正逻辑从嵌套中提取
3. ✅ **位置冲突解决**：避免`set_target_position()`覆盖问题
4. ✅ **代码优化**：简化嵌套结构，提高可读性
5. ✅ **功能测试**：完整的编译和功能验证

### 6.2 排除功能 (Out of Scope)
1. ❌ **新增传感器**：不涉及硬件传感器的添加
2. ❌ **算法优化**：不修改视角校正的核心算法
3. ❌ **界面改动**：不涉及用户界面的修改
4. ❌ **协议变更**：不修改通信协议和数据格式

## 7. 依赖与风险

### 7.1 内部依赖项
- **代码库**：FcSrc/User_Task.c 主状态机文件
- **PID控制**：FcSrc/User/PID.c 位置控制算法
- **状态管理**：现有的auto_pos_state状态机
- **调试系统**：AnoPTv8调试输出框架

### 7.2 外部依赖项
- **编译环境**：Keil μVision 5.06编译器
- **硬件平台**：STM32F429飞控系统
- **传感器**：MaixCAM视觉传感器数据

### 7.3 潜在风险
| 风险 | 概率 | 影响 | 缓解措施 |
|------|------|------|----------|
| 状态机逻辑错误 | 中 | 高 | 详细的状态转换测试 |
| 编译兼容性问题 | 低 | 中 | 使用现有编译环境验证 |
| 性能回归 | 低 | 中 | 性能基准测试对比 |
| 功能回归 | 中 | 高 | 完整的回归测试套件 |

## 8. 发布初步计划

### 8.1 开发阶段
1. **阶段1**：代码分析与方案设计（1天）
2. **阶段2**：状态机重构实现（2天）
3. **阶段3**：逻辑提取与优化（1天）
4. **阶段4**：编译验证与测试（1天）

### 8.2 质量保证
- **代码审查**：Bob架构师进行技术审查
- **功能测试**：Alex工程师进行完整测试
- **性能验证**：David数据分析师进行性能分析
- **文档更新**：Emma产品经理更新相关文档

### 8.3 发布标准
1. **编译通过**：Keil编译器零错误零警告
2. **功能验证**：视角校正功能100%可用
3. **性能达标**：响应时间<20ms，内存增加<4字节
4. **兼容性确认**：现有功能无回归问题

---

**文档状态：** 已完成  
**下一步行动：** 提交给Bob进行架构设计评审

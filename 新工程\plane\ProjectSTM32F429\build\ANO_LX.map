Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    main.o(.text) refers to drv_bsp.o(.text) for All_Init
    main.o(.text) refers to ano_scheduler.o(.text) for Scheduler_Setup
    ano_scheduler.o(.text) refers to pid.o(.text) for OutLoop_Control_Z
    ano_scheduler.o(.text) refers to user_task.o(.text) for LED_PWM_Control
    ano_scheduler.o(.text) refers to zigbee.o(.text) for zigbee_send_radar_coordinates
    ano_scheduler.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_ResetBits
    ano_scheduler.o(.text) refers to drv_sys.o(.text) for GetSysRunTimeMs
    ano_scheduler.o(.text) refers to ano_scheduler.o(.data) for _ledflag
    ano_scheduler.o(.data) refers to ano_scheduler.o(.text) for Loop_1000Hz
    user_task.o(.text) refers to anoptv8framefactory.o(.text) for AnoPTv8SendStr
    user_task.o(.text) refers to printfa.o(i.__0sprintf) for __2sprintf
    user_task.o(.text) refers to strcat.o(.text) for strcat
    user_task.o(.text) refers to user_task.o(.data) for LED_f
    user_task.o(.text) refers to pid.o(.bss) for PID_V
    user_task.o(.text) refers to user_task.o(.bss) for actual_work_pos
    user_task.o(.text) refers to pid.o(.text) for XY_flag_Control
    user_task.o(.text) refers to f2d.o(.text) for __aeabi_f2d
    user_task.o(.text) refers to lx_fcfunc.o(.text) for FC_Lock
    user_task.o(.text) refers to pid.o(.data) for target_pos
    user_task.o(.text) refers to drv_sys.o(.text) for GetSysRunTimeMs
    user_task.o(.text) refers to sqrtf.o(i.__hardfp_sqrtf) for __hardfp_sqrtf
    user_task.o(.text) refers to atan2f.o(i.__hardfp_atan2f) for __hardfp_atan2f
    user_task.o(.text) refers to mid360.o(.bss) for mid360
    user_task.o(.text) refers to tofmini.o(.bss) for g_tfmini_sensor
    user_task.o(.text) refers to user_task.o(.conststring) for .conststring
    user_task.o(.text) refers to zigbee.o(.text) for zigbee_send_screen_animal
    user_task.o(.text) refers to maixcam.o(.bss) for maixcam
    user_task.o(.text) refers to path_storage.o(.text) for find_precomputed_path
    user_task.o(.text) refers to maixcam.o(.text) for maixcam_get_x
    user_task.o(.text) refers to user_task.o(i.is_z_position_reached) for is_z_position_reached
    user_task.o(.text) refers to drv_bsp.o(.bss) for rc_in
    datatransfer.o(.text) refers to memcpya.o(.text) for __aeabi_memcpy
    datatransfer.o(.text) refers to anoptv8framefactory.o(.text) for AnoPTv8SendBuf
    datatransfer.o(.text) refers to datatransfer.o(.constdata) for ASFInfo
    datatransfer.o(.text) refers to lx_extsensor.o(.bss) for ext_sens
    datatransfer.o(.text) refers to drv_bsp.o(.bss) for rc_in
    datatransfer.o(.text) refers to lx_lowlevelfunc.o(.bss) for rt_tar
    datatransfer.o(.text) refers to mid360.o(.bss) for mid360
    datatransfer.o(.text) refers to pid.o(.data) for target_pos
    datatransfer.o(.text) refers to tofmini.o(.bss) for g_tfmini_sensor
    datatransfer.o(.text) refers to maixcam.o(.bss) for maixcam
    datatransfer.o(.text) refers to pid.o(.bss) for PID_V
    datatransfer.o(.text) refers to user_task.o(.data) for mission_step
    datatransfer.o(.text) refers to datatransfer.o(.bss) for ASFSta
    datatransfer.o(.text) refers to datatransfer.o(.data) for last_idx
    datatransfer.o(.text) refers to lx_fcstate.o(.bss) for fc_sta
    datatransfer.o(.text) refers to lx_lowlevelfunc.o(.data) for fc_vel
    lx_extsensor.o(.text) refers to mid360.o(.text) for mid360_is_data_valid
    lx_extsensor.o(.text) refers to datatransfer.o(.text) for AnoDTLxFrameSendTrigger
    lx_extsensor.o(.text) refers to tofmini.o(.text) for tofmini_is_distance_valid
    lx_extsensor.o(.text) refers to mid360.o(.bss) for mid360
    lx_extsensor.o(.text) refers to lx_extsensor.o(.bss) for ext_sens
    lx_fcfunc.o(.text) refers to anoptv8cmd.o(.text) for AnoPTv8CmdSendIsInIdle
    lx_fcfunc.o(.text) refers to lx_fcstate.o(.bss) for fc_sta
    lx_fcfunc.o(.text) refers to lx_fcfunc.o(.data) for old_mode
    lx_fcstate.o(.text) refers to lx_fcfunc.o(.text) for FC_Unlock
    lx_fcstate.o(.text) refers to drv_bsp.o(.bss) for rc_in
    lx_fcstate.o(.text) refers to lx_fcstate.o(.data) for sti_fun
    lx_fcstate.o(.text) refers to lx_fcstate.o(.bss) for fc_sta
    lx_lowlevelfunc.o(.text) refers to lx_fcfunc.o(.text) for LX_Change_Mode
    lx_lowlevelfunc.o(.text) refers to ano_math.o(.text) for my_deadzone
    lx_lowlevelfunc.o(.text) refers to datatransfer.o(.text) for AnoDTLxFrameSendTrigger
    lx_lowlevelfunc.o(.text) refers to drv_pwmout.o(.text) for DrvMotorPWMSet
    lx_lowlevelfunc.o(.text) refers to drv_bsp.o(.text) for DrvRcInputTask
    lx_lowlevelfunc.o(.text) refers to lx_fcstate.o(.text) for LX_FC_State_Task
    lx_lowlevelfunc.o(.text) refers to mid360.o(.text) for mid360_check_state
    lx_lowlevelfunc.o(.text) refers to drv_bsp.o(.bss) for rc_in
    lx_lowlevelfunc.o(.text) refers to drv_bsp.o(.data) for EmergencyStopESC
    lx_lowlevelfunc.o(.text) refers to lx_lowlevelfunc.o(.data) for mod_f
    lx_lowlevelfunc.o(.text) refers to lx_fcstate.o(.data) for sti_fun
    lx_lowlevelfunc.o(.text) refers to lx_lowlevelfunc.o(.bss) for rt_tar
    lx_lowlevelfunc.o(.text) refers to pid.o(.bss) for PID_V
    lx_lowlevelfunc.o(.text) refers to lx_fcstate.o(.bss) for fc_sta
    lx_lowlevelfunc.o(.text) refers to drv_uart.o(.text) for DrvUartDataCheck
    lx_lowlevelfunc.o(.text) refers to drv_usb.o(.text) for DrvUsbRunTask1MS
    lx_lowlevelfunc.o(.text) refers to anoptv8exapi.o(.text) for AnoPTv8HwTrigger1ms
    lx_lowlevelfunc.o(.text) refers to lx_extsensor.o(.text) for LX_FC_EXT_Sensor_Task
    crc.o(.text) refers to crc.o(.data) for Crc32Table
    g_port.o(.text) refers to crc.o(.text) for crc_32
    g_port.o(.text) refers to drv_uart.o(.text) for DrvUart4SendBuf
    g_port.o(.text) refers to g_port.o(.bss) for control_data
    g_port.o(.text) refers to g_port.o(.data) for parse_state
    mid360.o(.text) refers to mid360.o(.data) for my_flag
    mid360.o(.text) refers to mid360.o(.bss) for debug_info
    pid.o(.text) refers to pid.o(.data) for AIRCRAFT_X_PID_KP
    pid.o(.text) refers to pid.o(.bss) for X_PID
    pid.o(.text) refers to tofmini.o(.bss) for g_tfmini_sensor
    pid.o(.text) refers to mid360.o(.bss) for mid360
    pid.o(.text) refers to drv_sys.o(.text) for GetSysRunTimeMs
    pid.o(.text) refers to maixcam.o(.bss) for maixcam
    pid.o(.text) refers to lx_lowlevelfunc.o(.bss) for rt_tar
    pid.o(.text) refers to g_port.o(.text) for GimbalAngleControl
    pid.o(.text) refers to g_port.o(.bss) for gimbal_angle_data
    pid.o(.text) refers to dflti.o(.text) for __aeabi_i2d
    pid.o(.text) refers to ano_math.o(.text) for my_cos_deg
    pid.o(.text) refers to d2f.o(.text) for __aeabi_d2f
    zigbee.o(.text) refers to anoptv8framefactory.o(.text) for AnoPTv8SendStr
    zigbee.o(.text) refers to drv_uart.o(.text) for DrvUart5SendBuf
    zigbee.o(.text) refers to zigbee.o(.data) for calibration_applied
    zigbee.o(.text) refers to user_task.o(.data) for work_pos
    zigbee.o(.text) refers to printfa.o(i.__0sprintf) for __2sprintf
    zigbee.o(.text) refers to zigbee.o(.bss) for screen_receive_buf
    zigbee.o(.text) refers to mid360.o(.bss) for mid360
    zigbee.o(.text) refers to mid360.o(.text) for mid360_is_data_valid
    zigbee.o(.text) refers to drv_sys.o(.text) for GetSysRunTimeMs
    maixcam.o(.text) refers to drv_sys.o(.text) for GetSysRunTimeMs
    maixcam.o(.text) refers to drv_uart.o(.text) for DrvUart2SendBuf
    maixcam.o(.text) refers to maixcam.o(.data) for maixcam_flag
    maixcam.o(.text) refers to maixcam.o(.bss) for maixcam
    tofsense-m.o(.text) refers to drv_uart.o(.text) for DrvUart3SendBuf
    tofsense-m.o(.text) refers to tofsense-m.o(.bss) for tof_sensors
    tofsense-m.o(.text) refers to tofsense-m.o(.data) for current_query_sensor
    tofmini.o(.text) refers to tofmini.o(.bss) for g_tfmini_parser
    path_storage.o(.text) refers to anoptv8framefactory.o(.text) for AnoPTv8SendStr
    path_storage.o(.text) refers to path_storage.o(.constdata) for path_lookup_table
    path_storage.o(.text) refers to path_storage.o(.conststring) for .conststring
    anoptv8exapi.o(.text) refers to drv_uart.o(.text) for DrvUart1SendBuf
    anoptv8exapi.o(.text) refers to drv_usb.o(.text) for DrvUsbCdcAddTxData
    anoptv8exapi.o(.text) refers to anoptv8run.o(.text) for AnoPTv8RunThread1ms
    anoptv8exapi.o(.text) refers to anoptv8par.o(.text) for AnoPTv8ParRegister
    anoptv8exapi.o(.text) refers to anoptv8framefactory.o(.text) for AnoPTv8SendStr
    anoptv8exapi.o(.text) refers to memcpya.o(.text) for __aeabi_memcpy4
    anoptv8exapi.o(.text) refers to drv_bsp.o(.text) for DrvSbusGetOneByte
    anoptv8exapi.o(.text) refers to anoptv8cmd.o(.text) for AnoPTv8CmdValCpy
    anoptv8exapi.o(.text) refers to anoptv8exapi.o(.constdata) for _parInfoList
    anoptv8exapi.o(.text) refers to anoptv8exapi.o(.data) for testPar_u8
    anoptv8exapi.o(.text) refers to pid.o(.bss) for X_PID
    anoptv8exapi.o(.text) refers to datatransfer.o(.text) for AnoDTLxFrameAnl
    anoptv8exapi.o(.text) refers to drv_anoof.o(.text) for AnoOFFrameAnl
    anoptv8exapi.o(.constdata) refers to anoptv8exapi.o(.data) for testPar_u8
    anoptv8exapi.o(.constdata) refers to anoptv8exapi.o(.text) for testFun
    anoptv8run.o(.text) refers to anoptv8exapi.o(.text) for AnoPTv8HwSendBytes
    anoptv8run.o(.text) refers to anoptv8cmd.o(.text) for AnoPTv8CmdRunThread1ms
    anoptv8run.o(.text) refers to anoptv8par.o(.text) for AnoPTv8ParFrameAnl
    anoptv8run.o(.text) refers to anoptv8run.o(.bss) for AnoPTv8TxBuf
    anoptv8run.o(.text) refers to anoptv8run.o(.data) for lastSequence
    anoptv8cmd.o(.text) refers to memcpya.o(.text) for __aeabi_memcpy
    anoptv8cmd.o(.text) refers to anoptv8framefactory.o(.text) for AnoPTv8SendCheck
    anoptv8cmd.o(.text) refers to anoptv8run.o(.text) for AnoPTv8GetFreeTxBufIndex
    anoptv8cmd.o(.text) refers to anoptv8cmd.o(.data) for cmdCount
    anoptv8cmd.o(.text) refers to anoptv8cmd.o(.bss) for pCmdInfoList
    anoptv8cmd.o(.text) refers to anoptv8run.o(.bss) for AnoPTv8TxBuf
    anoptv8par.o(.text) refers to ffltl.o(.text) for __aeabi_l2f
    anoptv8par.o(.text) refers to dfltl.o(.text) for __aeabi_l2d
    anoptv8par.o(.text) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    anoptv8par.o(.text) refers to cdcmple.o(.text) for __aeabi_cdcmple
    anoptv8par.o(.text) refers to memseta.o(.text) for __aeabi_memclr
    anoptv8par.o(.text) refers to anoptv8par.o(.data) for parCount
    anoptv8par.o(.text) refers to anoptv8par.o(.bss) for pParInfoList
    anoptv8par.o(.text) refers to memcpya.o(.text) for __aeabi_memcpy
    anoptv8par.o(.text) refers to anoptv8framefactory.o(.text) for AnoPTv8SendDevInfo
    anoptv8par.o(.text) refers to anoptv8exapi.o(.text) for AnoPTv8HwParCmdRecvCallback
    anoptv8framefactory.o(.text) refers to anoptv8run.o(.text) for AnoPTv8GetFreeTxBufIndex
    anoptv8framefactory.o(.text) refers to anoptv8run.o(.bss) for AnoPTv8TxBuf
    anoptv8framefactory.o(.text) refers to anoptv8par.o(.text) for AnoPTv8ParGetCount
    anoptv8framefactory.o(.text) refers to anoptv8cmd.o(.text) for AnoPTv8CmdGetCount
    anoptv8framefactory.o(.text) refers to memcpya.o(.text) for __aeabi_memcpy
    drv_bsp.o(.text) refers to drv_rcin.o(.text) for DrvRcSbusInit
    drv_bsp.o(.text) refers to drv_sys.o(.text) for DrvSysInit
    drv_bsp.o(.text) refers to drv_usb.o(.text) for DrvUsbInit
    drv_bsp.o(.text) refers to drv_led.o(.text) for DvrLedInit
    drv_bsp.o(.text) refers to drv_pwmout.o(.text) for DrvPwmOutInit
    drv_bsp.o(.text) refers to anoptv8exapi.o(.text) for AnoPTv8ParInit
    drv_bsp.o(.text) refers to drv_uart.o(.text) for DrvUart1Init
    drv_bsp.o(.text) refers to tofmini.o(.text) for tofmini_init
    drv_bsp.o(.text) refers to drv_timer.o(.text) for DrvTimerFcInit
    drv_bsp.o(.text) refers to drv_adc.o(.text) for DrvAdcInit
    drv_bsp.o(.text) refers to pid.o(.text) for PID_Init
    drv_bsp.o(.text) refers to user_task.o(.text) for jiguang
    drv_bsp.o(.text) refers to memcpya.o(.text) for __aeabi_memcpy
    drv_bsp.o(.text) refers to drv_bsp.o(.bss) for rc_in
    drv_bsp.o(.text) refers to drv_bsp.o(.data) for ch_sta
    ano_math.o(.text) refers to cdcmple.o(.text) for __aeabi_cdcmple
    ano_math.o(.text) refers to dmul.o(.text) for __aeabi_dmul
    ano_math.o(.text) refers to dadd.o(.text) for __aeabi_dadd
    ano_math.o(.text) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    ano_math.o(.text) refers to dflti.o(.text) for __aeabi_i2d
    ano_math.o(.text) refers to d2f.o(.text) for __aeabi_d2f
    ano_math.o(.text) refers to ano_math.o(.constdata) for fast_atan_table
    drv_anoof.o(.text) refers to drv_anoof.o(.bss) for check_time_ms
    drv_ubloxgps.o(.text) refers to datatransfer.o(.text) for AnoDTLxFrameSendTrigger
    drv_ubloxgps.o(.text) refers to drv_uart.o(.text) for DrvUart3SendBuf
    drv_ubloxgps.o(.text) refers to drv_sys.o(.text) for MyDelayMs
    drv_ubloxgps.o(.text) refers to drv_ubloxgps.o(.data) for pvt_receive_updata
    drv_ubloxgps.o(.text) refers to drv_ubloxgps.o(.bss) for ubx
    drv_ubloxgps.o(.text) refers to lx_extsensor.o(.bss) for ext_sens
    drvanoof_ptv7.o(.text) refers to drvanoof_ptv7.o(.bss) for check_time_ms
    drvanoof_ptv7.o(.text) refers to drv_anoof.o(.bss) for ano_of
    drvanoof_ptv7.o(.text) refers to drvanoof_ptv7.o(.data) for rxstate
    stm32f4xx_it.o(.text) refers to stm32f4xx_exti.o(.text) for EXTI_GetITStatus
    stm32f4xx_it.o(.text) refers to drv_rcin.o(.text) for PPM_IRQH
    stm32f4xx_it.o(.text) refers to stm32f4xx_tim.o(.text) for TIM_GetITStatus
    stm32f4xx_it.o(.text) refers to lx_lowlevelfunc.o(.text) for ANO_LX_Task
    stm32f4xx_it.o(.text) refers to drv_uart.o(.text) for Usart1_IRQ
    drv_sys.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_GetClocksFreq
    drv_sys.o(.text) refers to misc.o(.text) for SysTick_CLKSourceConfig
    drv_sys.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_StructInit
    drv_sys.o(.text) refers to drv_sys.o(.data) for SysRunTimeMs
    drv_led.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_StructInit
    drv_led.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphClockCmd
    drv_timer.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB1PeriphClockCmd
    drv_timer.o(.text) refers to stm32f4xx_tim.o(.text) for TIM_DeInit
    drv_timer.o(.text) refers to misc.o(.text) for NVIC_Init
    drv_uart.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphClockCmd
    drv_uart.o(.text) refers to misc.o(.text) for NVIC_Init
    drv_uart.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_PinAFConfig
    drv_uart.o(.text) refers to stm32f4xx_usart.o(.text) for USART_StructInit
    drv_uart.o(.text) refers to anoptv8exapi.o(.text) for AnoPTv8HwRecvByte
    drv_uart.o(.text) refers to drv_uart.o(.data) for U1TxInCnt
    drv_uart.o(.text) refers to drv_uart.o(.bss) for U1TxBuf
    drv_uart.o(.text) refers to tofmini.o(.text) for TOFmini_RecvOneByte
    drv_uart.o(.text) refers to mid360.o(.text) for mid360_GetOneByte
    drv_uart.o(.text) refers to zigbee.o(.text) for screen_receiver_GetOneByte
    drv_uart.o(.text) refers to maixcam.o(.text) for maixcam_receiver_GetOneByte
    drv_pwmout.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_StructInit
    drv_pwmout.o(.text) refers to stm32f4xx_tim.o(.text) for TIM_TimeBaseStructInit
    drv_pwmout.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB1PeriphClockCmd
    drv_pwmout.o(.text) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    drv_pwmout.o(.text) refers to drv_bsp.o(.data) for EmergencyStopESC
    drv_dshot600.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphClockCmd
    drv_dshot600.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_Init
    drv_dshot600.o(.text) refers to stm32f4xx_tim.o(.text) for TIM_TimeBaseInit
    drv_dshot600.o(.text) refers to stm32f4xx_dma.o(.text) for DMA_StructInit
    drv_dshot600.o(.text) refers to drv_dshot600.o(.bss) for dma_buffer
    drv_rcin.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_StructInit
    drv_rcin.o(.text) refers to stm32f4xx_tim.o(.text) for TIM_ICStructInit
    drv_rcin.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB1PeriphClockCmd
    drv_rcin.o(.text) refers to misc.o(.text) for NVIC_Init
    drv_rcin.o(.text) refers to drv_bsp.o(.text) for DrvPpmGetOneCh
    drv_rcin.o(.text) refers to stm32f4xx_usart.o(.text) for USART_Init
    drv_rcin.o(.text) refers to memcpya.o(.text) for __aeabi_memcpy
    drv_rcin.o(.text) refers to drv_rcin.o(.data) for temp_cnt
    drv_rcin.o(.text) refers to drv_rcin.o(.bss) for crsf_rx_buffer
    drv_rcin.o(.text) refers to drv_bsp.o(.bss) for rc_in
    drv_usb.o(.text) refers to usb_dc_dwc2.o(.text) for usbd_ep_start_read
    drv_usb.o(.text) refers to anoptv8exapi.o(.text) for AnoPTv8HwRecvByte
    drv_usb.o(.text) refers to drv_sys.o(.text) for MyDelayMs
    drv_usb.o(.text) refers to usbd_core.o(.text) for usbd_desc_register
    drv_usb.o(.text) refers to usbd_cdc.o(.text) for usbd_cdc_acm_init_intf
    drv_usb.o(.text) refers to drv_usb.o(.data) for ep_tx_busy_flag
    drv_usb.o(.text) refers to drv_usb.o(.noncacheable) for cdcReadBuf
    drv_usb.o(.text) refers to drv_usb.o(.constdata) for cdc_descriptor
    drv_usb.o(.text) refers to drv_usb.o(.bss) for intf0
    drv_usb.o(.data) refers to drv_usb.o(.text) for usbd_cdc_acm_bulk_out
    drv_adc.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphClockCmd
    drv_adc.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_Init
    drv_adc.o(.text) refers to stm32f4xx_adc.o(.text) for ADC_CommonInit
    drv_adc.o(.text) refers to stm32f4xx_dma.o(.text) for DMA_StructInit
    drv_adc.o(.text) refers to drv_adc.o(.bss) for adcBuffer
    drv_adc.o(.text) refers to drv_adc.o(.data) for AdcVal_Bat
    system_stm32f4xx.o(.text) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    misc.o(.text) refers to main.o(.text) for assert_failed
    misc.o(.text) refers to misc.o(.conststring) for .conststring
    stm32f4xx_adc.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f4xx_adc.o(.text) refers to main.o(.text) for assert_failed
    stm32f4xx_adc.o(.text) refers to stm32f4xx_adc.o(.conststring) for .conststring
    stm32f4xx_dma.o(.text) refers to main.o(.text) for assert_failed
    stm32f4xx_dma.o(.text) refers to stm32f4xx_dma.o(.conststring) for .conststring
    stm32f4xx_exti.o(.text) refers to main.o(.text) for assert_failed
    stm32f4xx_exti.o(.text) refers to stm32f4xx_exti.o(.conststring) for .conststring
    stm32f4xx_flash.o(.text) refers to main.o(.text) for assert_failed
    stm32f4xx_flash.o(.text) refers to stm32f4xx_flash.o(.conststring) for .conststring
    stm32f4xx_gpio.o(.text) refers to main.o(.text) for assert_failed
    stm32f4xx_gpio.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphResetCmd
    stm32f4xx_gpio.o(.text) refers to stm32f4xx_gpio.o(.conststring) for .conststring
    stm32f4xx_rcc.o(.text) refers to main.o(.text) for assert_failed
    stm32f4xx_rcc.o(.text) refers to stm32f4xx_rcc.o(.conststring) for .conststring
    stm32f4xx_rcc.o(.text) refers to stm32f4xx_rcc.o(.data) for APBAHBPrescTable
    stm32f4xx_spi.o(.text) refers to main.o(.text) for assert_failed
    stm32f4xx_spi.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f4xx_spi.o(.text) refers to stm32f4xx_spi.o(.conststring) for .conststring
    stm32f4xx_tim.o(.text) refers to main.o(.text) for assert_failed
    stm32f4xx_tim.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f4xx_tim.o(.text) refers to stm32f4xx_tim.o(.conststring) for .conststring
    stm32f4xx_usart.o(.text) refers to main.o(.text) for assert_failed
    stm32f4xx_usart.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f4xx_usart.o(.text) refers to stm32f4xx_usart.o(.conststring) for .conststring
    startup_stm32f429_439xx.o(RESET) refers to startup_stm32f429_439xx.o(STACK) for __initial_sp
    startup_stm32f429_439xx.o(RESET) refers to startup_stm32f429_439xx.o(.text) for Reset_Handler
    startup_stm32f429_439xx.o(RESET) refers to stm32f4xx_it.o(.text) for NMI_Handler
    startup_stm32f429_439xx.o(RESET) refers to drv_sys.o(.text) for SysTick_Handler
    startup_stm32f429_439xx.o(RESET) refers to usb_dc_dwc2.o(.text) for OTG_FS_IRQHandler
    startup_stm32f429_439xx.o(.text) refers to system_stm32f4xx.o(.text) for SystemInit
    startup_stm32f429_439xx.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    usb_dc_dwc2.o(.text) refers to memseta.o(.text) for __aeabi_memclr4
    usb_dc_dwc2.o(.text) refers to usb_dc_dwc2.o(.noncacheable) for g_dwc2_udc
    usb_dc_dwc2.o(.text) refers to usbd_core.o(.text) for usbd_event_ep_out_complete_handler
    usb_dc_dwc2.o(.text) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    usbd_cdc.o(.text) refers to memcpya.o(.text) for __aeabi_memcpy
    usbd_cdc.o(.text) refers to memcmp.o(.text) for memcmp
    usbd_cdc.o(.data) refers to usbd_cdc.o(.conststring) for .conststring
    usbd_core.o(.text) refers to usb_dc_dwc2.o(.text) for usbd_ep_open
    usbd_core.o(.text) refers to memcpya.o(.text) for __aeabi_memcpy
    usbd_core.o(.text) refers to drv_usb.o(.text) for usbd_configure_done_callback
    usbd_core.o(.text) refers to usbd_core.o(.noncacheable) for usbd_core_cfg
    usbd_core.o(.text) refers to usbd_core.o(.data) for msosv1_desc
    usbd_core.o(.text) refers to usbd_core.o(.bss) for tx_msg
    usbd_core.o(.text) refers to memseta.o(.text) for __aeabi_memclr4
    atan2f.o(i.__hardfp_atan2f) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2f.o(i.__hardfp_atan2f) refers to fpclassifyf.o(i.__ARM_fpclassifyf) for __ARM_fpclassifyf
    atan2f.o(i.__hardfp_atan2f) refers to funder.o(i.__mathlib_flt_underflow) for __mathlib_flt_underflow
    atan2f.o(i.__hardfp_atan2f) refers to errno.o(i.__set_errno) for __set_errno
    atan2f.o(i.__hardfp_atan2f) refers to funder.o(i.__mathlib_flt_infnan2) for __mathlib_flt_infnan2
    atan2f.o(i.__softfp_atan2f) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2f.o(i.__softfp_atan2f) refers to atan2f.o(i.__hardfp_atan2f) for __hardfp_atan2f
    atan2f.o(i.atan2f) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2f.o(i.atan2f) refers to atan2f.o(i.__hardfp_atan2f) for __hardfp_atan2f
    atan2f_x.o(i.____hardfp_atan2f$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2f_x.o(i.____hardfp_atan2f$lsc) refers to fpclassifyf.o(i.__ARM_fpclassifyf) for __ARM_fpclassifyf
    atan2f_x.o(i.____hardfp_atan2f$lsc) refers to errno.o(i.__set_errno) for __set_errno
    atan2f_x.o(i.____hardfp_atan2f$lsc) refers to funder.o(i.__mathlib_flt_infnan2) for __mathlib_flt_infnan2
    atan2f_x.o(i.____softfp_atan2f$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2f_x.o(i.____softfp_atan2f$lsc) refers to atan2f_x.o(i.____hardfp_atan2f$lsc) for ____hardfp_atan2f$lsc
    atan2f_x.o(i.__atan2f$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2f_x.o(i.__atan2f$lsc) refers to atan2f_x.o(i.____hardfp_atan2f$lsc) for ____hardfp_atan2f$lsc
    sqrtf.o(i.__hardfp_sqrtf) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrtf.o(i.__hardfp_sqrtf) refers to errno.o(i.__set_errno) for __set_errno
    sqrtf.o(i.__softfp_sqrtf) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrtf.o(i.__softfp_sqrtf) refers to errno.o(i.__set_errno) for __set_errno
    sqrtf.o(i.sqrtf) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrtf.o(i.sqrtf) refers to errno.o(i.__set_errno) for __set_errno
    sqrtf_x.o(i.____hardfp_sqrtf$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrtf_x.o(i.____hardfp_sqrtf$lsc) refers to errno.o(i.__set_errno) for __set_errno
    sqrtf_x.o(i.____softfp_sqrtf$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrtf_x.o(i.____softfp_sqrtf$lsc) refers to errno.o(i.__set_errno) for __set_errno
    sqrtf_x.o(i.__sqrtf$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrtf_x.o(i.__sqrtf$lsc) refers to errno.o(i.__set_errno) for __set_errno
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000F) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$00000011) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry12b.o(.ARM.Collect$$$$0000000E) for __rt_lib_shutdown_fini
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    printfb.o(i.__0fprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0fprintf$bare) refers to fputc.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0printf$bare) refers to fputc.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printfb.o(i.__0vfprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vfprintf$bare) refers to fputc.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vprintf$bare) refers to fputc.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printf0.o(i.__0fprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0fprintf$0) refers to fputc.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0printf$0) refers to fputc.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf0.o(i.__0vfprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vfprintf$0) refers to fputc.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vprintf$0) refers to fputc.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf1.o(i.__0fprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0fprintf$1) refers to fputc.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0printf$1) refers to fputc.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i.__0vfprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vfprintf$1) refers to fputc.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vprintf$1) refers to fputc.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf2.o(i.__0fprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0fprintf$2) refers to fputc.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0printf$2) refers to fputc.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf2.o(i.__0vfprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vfprintf$2) refers to fputc.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vprintf$2) refers to fputc.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf3.o(i.__0fprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0fprintf$3) refers to fputc.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0printf$3) refers to fputc.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i.__0vfprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vfprintf$3) refers to fputc.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vprintf$3) refers to fputc.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf4.o(i.__0fprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0fprintf$4) refers to fputc.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0printf$4) refers to fputc.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i.__0vfprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vfprintf$4) refers to fputc.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vprintf$4) refers to fputc.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf5.o(i.__0fprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0fprintf$5) refers to fputc.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0printf$5) refers to fputc.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i.__0vfprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vfprintf$5) refers to fputc.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vprintf$5) refers to fputc.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf6.o(i.__0fprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0fprintf$6) refers to fputc.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0printf$6) refers to fputc.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i.__0vfprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vfprintf$6) refers to fputc.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vprintf$6) refers to fputc.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i._printf_core) refers to printf6.o(i._printf_pre_padding) for _printf_pre_padding
    printf6.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf6.o(i._printf_core) refers to printf6.o(i._printf_post_padding) for _printf_post_padding
    printf7.o(i.__0fprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0fprintf$7) refers to fputc.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0printf$7) refers to fputc.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i.__0vfprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vfprintf$7) refers to fputc.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vprintf$7) refers to fputc.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i._printf_core) refers to printf7.o(i._printf_pre_padding) for _printf_pre_padding
    printf7.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf7.o(i._printf_core) refers to printf7.o(i._printf_post_padding) for _printf_post_padding
    printf8.o(i.__0fprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0fprintf$8) refers to fputc.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0printf$8) refers to fputc.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i.__0vfprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vfprintf$8) refers to fputc.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vprintf$8) refers to fputc.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i._printf_core) refers to printf8.o(i._printf_pre_padding) for _printf_pre_padding
    printf8.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf8.o(i._printf_core) refers to printf8.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i.__0fprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0fprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0fprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0printf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0printf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0snprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0snprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0snprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0sprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0sprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0sprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i.__0vfprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vfprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vfprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0vsnprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0vsprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i._fp_digits) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._fp_digits) refers to dmul.o(.text) for __aeabi_dmul
    printfa.o(i._fp_digits) refers to ddiv.o(.text) for __aeabi_ddiv
    printfa.o(i._fp_digits) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    printfa.o(i._fp_digits) refers to dadd.o(.text) for __aeabi_dadd
    printfa.o(i._fp_digits) refers to dfixul.o(.text) for __aeabi_d2ulz
    printfa.o(i._fp_digits) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_core) refers to printfa.o(i._printf_pre_padding) for _printf_pre_padding
    printfa.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers to printfa.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i._printf_core) refers to printfa.o(i._fp_digits) for _fp_digits
    printfa.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printfa.o(i._printf_post_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_pre_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._snputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._sputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    dadd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ffltl.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ffltl.o(.text) refers to fepilogue.o(.text) for _float_epilogue
    dflti.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dflti.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dfltl.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfltl.o(.text) refers to depilogue.o(.text) for _double_epilogue
    f2d.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    cdcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    cdrcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers to fepilogue.o(.text) for _float_round
    fpclassifyf.o(i.__ARM_fpclassifyf) refers (Special) to iusefp.o(.text) for __I$use$fp
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_stm32f429_439xx.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_stm32f429_439xx.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(.text) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(.text) for main
    fputc.o(i.fputc) refers (Special) to iusesemip.o(.text) for __I$use$semihosting$fputc
    fputc.o(i.fputc) refers (Special) to semi.o(.text) for __semihosting_library_function
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    errno.o(i.__aeabi_errno_addr) refers to errno.o(.data) for .data
    errno.o(i.__read_errno) refers to errno.o(.data) for .data
    errno.o(i.__set_errno) refers to errno.o(.data) for .data
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    dfixul.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload
    user_task.o(i.is_z_position_reached) refers to pid.o(.bss) for PID_V


==============================================================================

Removing Unused input sections from the image.

    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.rrx_text), (6 bytes).
    Removing ano_scheduler.o(.rev16_text), (4 bytes).
    Removing ano_scheduler.o(.revsh_text), (4 bytes).
    Removing ano_scheduler.o(.rrx_text), (6 bytes).
    Removing user_task.o(.rev16_text), (4 bytes).
    Removing user_task.o(.revsh_text), (4 bytes).
    Removing user_task.o(.rrx_text), (6 bytes).
    Removing datatransfer.o(.rev16_text), (4 bytes).
    Removing datatransfer.o(.revsh_text), (4 bytes).
    Removing datatransfer.o(.rrx_text), (6 bytes).
    Removing lx_extsensor.o(.rev16_text), (4 bytes).
    Removing lx_extsensor.o(.revsh_text), (4 bytes).
    Removing lx_extsensor.o(.rrx_text), (6 bytes).
    Removing lx_fcfunc.o(.rev16_text), (4 bytes).
    Removing lx_fcfunc.o(.revsh_text), (4 bytes).
    Removing lx_fcfunc.o(.rrx_text), (6 bytes).
    Removing lx_fcstate.o(.rev16_text), (4 bytes).
    Removing lx_fcstate.o(.revsh_text), (4 bytes).
    Removing lx_fcstate.o(.rrx_text), (6 bytes).
    Removing lx_lowlevelfunc.o(.rev16_text), (4 bytes).
    Removing lx_lowlevelfunc.o(.revsh_text), (4 bytes).
    Removing lx_lowlevelfunc.o(.rrx_text), (6 bytes).
    Removing crc.o(.rev16_text), (4 bytes).
    Removing crc.o(.revsh_text), (4 bytes).
    Removing crc.o(.rrx_text), (6 bytes).
    Removing g_port.o(.rev16_text), (4 bytes).
    Removing g_port.o(.revsh_text), (4 bytes).
    Removing g_port.o(.rrx_text), (6 bytes).
    Removing mid360.o(.rev16_text), (4 bytes).
    Removing mid360.o(.revsh_text), (4 bytes).
    Removing mid360.o(.rrx_text), (6 bytes).
    Removing pid.o(.rev16_text), (4 bytes).
    Removing pid.o(.revsh_text), (4 bytes).
    Removing pid.o(.rrx_text), (6 bytes).
    Removing zigbee.o(.rev16_text), (4 bytes).
    Removing zigbee.o(.revsh_text), (4 bytes).
    Removing zigbee.o(.rrx_text), (6 bytes).
    Removing maixcam.o(.rev16_text), (4 bytes).
    Removing maixcam.o(.revsh_text), (4 bytes).
    Removing maixcam.o(.rrx_text), (6 bytes).
    Removing tofsense-m.o(.rev16_text), (4 bytes).
    Removing tofsense-m.o(.revsh_text), (4 bytes).
    Removing tofsense-m.o(.rrx_text), (6 bytes).
    Removing tofsense-m.o(.text), (2248 bytes).
    Removing tofsense-m.o(.bss), (944 bytes).
    Removing tofsense-m.o(.data), (12 bytes).
    Removing tofmini.o(.rev16_text), (4 bytes).
    Removing tofmini.o(.revsh_text), (4 bytes).
    Removing tofmini.o(.rrx_text), (6 bytes).
    Removing path_storage.o(.rev16_text), (4 bytes).
    Removing path_storage.o(.revsh_text), (4 bytes).
    Removing path_storage.o(.rrx_text), (6 bytes).
    Removing anoptv8exapi.o(.rev16_text), (4 bytes).
    Removing anoptv8exapi.o(.revsh_text), (4 bytes).
    Removing anoptv8exapi.o(.rrx_text), (6 bytes).
    Removing drv_bsp.o(.rev16_text), (4 bytes).
    Removing drv_bsp.o(.revsh_text), (4 bytes).
    Removing drv_bsp.o(.rrx_text), (6 bytes).
    Removing ano_math.o(.rev16_text), (4 bytes).
    Removing ano_math.o(.revsh_text), (4 bytes).
    Removing ano_math.o(.rrx_text), (6 bytes).
    Removing drv_anoof.o(.rev16_text), (4 bytes).
    Removing drv_anoof.o(.revsh_text), (4 bytes).
    Removing drv_anoof.o(.rrx_text), (6 bytes).
    Removing drv_ubloxgps.o(.rev16_text), (4 bytes).
    Removing drv_ubloxgps.o(.revsh_text), (4 bytes).
    Removing drv_ubloxgps.o(.rrx_text), (6 bytes).
    Removing drv_ubloxgps.o(.text), (932 bytes).
    Removing drv_ubloxgps.o(.bss), (100 bytes).
    Removing drv_ubloxgps.o(.data), (182 bytes).
    Removing drvanoof_ptv7.o(.rev16_text), (4 bytes).
    Removing drvanoof_ptv7.o(.revsh_text), (4 bytes).
    Removing drvanoof_ptv7.o(.rrx_text), (6 bytes).
    Removing drvanoof_ptv7.o(.text), (836 bytes).
    Removing drvanoof_ptv7.o(.bss), (62 bytes).
    Removing drvanoof_ptv7.o(.data), (3 bytes).
    Removing stm32f4xx_it.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_it.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_it.o(.rrx_text), (6 bytes).
    Removing drv_sys.o(.rev16_text), (4 bytes).
    Removing drv_sys.o(.revsh_text), (4 bytes).
    Removing drv_sys.o(.rrx_text), (6 bytes).
    Removing drv_led.o(.rev16_text), (4 bytes).
    Removing drv_led.o(.revsh_text), (4 bytes).
    Removing drv_led.o(.rrx_text), (6 bytes).
    Removing drv_timer.o(.rev16_text), (4 bytes).
    Removing drv_timer.o(.revsh_text), (4 bytes).
    Removing drv_timer.o(.rrx_text), (6 bytes).
    Removing drv_uart.o(.rev16_text), (4 bytes).
    Removing drv_uart.o(.revsh_text), (4 bytes).
    Removing drv_uart.o(.rrx_text), (6 bytes).
    Removing drv_pwmout.o(.rev16_text), (4 bytes).
    Removing drv_pwmout.o(.revsh_text), (4 bytes).
    Removing drv_pwmout.o(.rrx_text), (6 bytes).
    Removing drv_dshot600.o(.rev16_text), (4 bytes).
    Removing drv_dshot600.o(.revsh_text), (4 bytes).
    Removing drv_dshot600.o(.rrx_text), (6 bytes).
    Removing drv_dshot600.o(.text), (740 bytes).
    Removing drv_dshot600.o(.bss), (144 bytes).
    Removing drv_rcin.o(.rev16_text), (4 bytes).
    Removing drv_rcin.o(.revsh_text), (4 bytes).
    Removing drv_rcin.o(.rrx_text), (6 bytes).
    Removing drv_usb.o(.rev16_text), (4 bytes).
    Removing drv_usb.o(.revsh_text), (4 bytes).
    Removing drv_usb.o(.rrx_text), (6 bytes).
    Removing drv_adc.o(.rev16_text), (4 bytes).
    Removing drv_adc.o(.revsh_text), (4 bytes).
    Removing drv_adc.o(.rrx_text), (6 bytes).
    Removing system_stm32f4xx.o(.rev16_text), (4 bytes).
    Removing system_stm32f4xx.o(.revsh_text), (4 bytes).
    Removing system_stm32f4xx.o(.rrx_text), (6 bytes).
    Removing misc.o(.rev16_text), (4 bytes).
    Removing misc.o(.revsh_text), (4 bytes).
    Removing misc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_adc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_adc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_adc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_dma.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dma.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dma.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_exti.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_exti.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_exti.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_flash.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_flash.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_flash.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_flash.o(.text), (2616 bytes).
    Removing stm32f4xx_flash.o(.conststring), (83 bytes).
    Removing stm32f4xx_gpio.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_gpio.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_gpio.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_rcc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_rcc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_rcc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_spi.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_spi.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_spi.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_spi.o(.text), (3436 bytes).
    Removing stm32f4xx_spi.o(.conststring), (81 bytes).
    Removing stm32f4xx_tim.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_tim.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_tim.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_usart.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_usart.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_usart.o(.rrx_text), (6 bytes).
    Removing startup_stm32f429_439xx.o(HEAP), (1024 bytes).
    Removing usbd_cdc.o(.conststring), (30 bytes).
    Removing usbd_cdc.o(.data), (32 bytes).

150 unused section(s) (total 14121 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/../cmprslib/zerorunl2.c          0x00000000   Number         0  __dczerorl2.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uidiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uldiv.o ABSOLUTE
    ../clib/microlib/errno.c                 0x00000000   Number         0  errno.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry12a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry12b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llsshr.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf3.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf2.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf1.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf0.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfb.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf7.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfa.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf8.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf6.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf5.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf4.o ABSOLUTE
    ../clib/microlib/printf/stubs.s          0x00000000   Number         0  stubs.o ABSOLUTE
    ../clib/microlib/stdio/fputc.c           0x00000000   Number         0  fputc.o ABSOLUTE
    ../clib/microlib/stdio/semi.s            0x00000000   Number         0  semi.o ABSOLUTE
    ../clib/microlib/stdio/streams.c         0x00000000   Number         0  stdout.o ABSOLUTE
    ../clib/microlib/string/memcmp.c         0x00000000   Number         0  memcmp.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpyb.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpya.o ABSOLUTE
    ../clib/microlib/string/memset.c         0x00000000   Number         0  memseta.o ABSOLUTE
    ../clib/microlib/string/strcat.c         0x00000000   Number         0  strcat.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusesemip.o ABSOLUTE
    ../fplib/microlib/d2f.c                  0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/microlib/f2d.c                  0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  dadd.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  depilogue.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  fepilogue.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixul.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dfltl.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dflti.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  ffltl.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  dmul.o ABSOLUTE
    ../mathlib/atan2f.c                      0x00000000   Number         0  atan2f.o ABSOLUTE
    ../mathlib/atan2f.c                      0x00000000   Number         0  atan2f_x.o ABSOLUTE
    ../mathlib/fpclassifyf.c                 0x00000000   Number         0  fpclassifyf.o ABSOLUTE
    ../mathlib/funder.c                      0x00000000   Number         0  funder.o ABSOLUTE
    ../mathlib/sqrtf.c                       0x00000000   Number         0  sqrtf.o ABSOLUTE
    ../mathlib/sqrtf.c                       0x00000000   Number         0  sqrtf_x.o ABSOLUTE
    ..\DriversBsp\Ano_Math.c                 0x00000000   Number         0  ano_math.o ABSOLUTE
    ..\DriversBsp\DrvAnoOF_ptv7.c            0x00000000   Number         0  drvanoof_ptv7.o ABSOLUTE
    ..\DriversBsp\Drv_AnoOf.c                0x00000000   Number         0  drv_anoof.o ABSOLUTE
    ..\DriversBsp\Drv_BSP.c                  0x00000000   Number         0  drv_bsp.o ABSOLUTE
    ..\DriversBsp\Drv_UbloxGPS.c             0x00000000   Number         0  drv_ubloxgps.o ABSOLUTE
    ..\DriversMcu\STM32F4xx\Drivers\Drv_Adc.c 0x00000000   Number         0  drv_adc.o ABSOLUTE
    ..\DriversMcu\STM32F4xx\Drivers\Drv_Dshot600.c 0x00000000   Number         0  drv_dshot600.o ABSOLUTE
    ..\DriversMcu\STM32F4xx\Drivers\Drv_PwmOut.c 0x00000000   Number         0  drv_pwmout.o ABSOLUTE
    ..\DriversMcu\STM32F4xx\Drivers\Drv_RcIn.c 0x00000000   Number         0  drv_rcin.o ABSOLUTE
    ..\DriversMcu\STM32F4xx\Drivers\Drv_Sys.c 0x00000000   Number         0  drv_sys.o ABSOLUTE
    ..\DriversMcu\STM32F4xx\Drivers\Drv_Timer.c 0x00000000   Number         0  drv_timer.o ABSOLUTE
    ..\DriversMcu\STM32F4xx\Drivers\Drv_Uart.c 0x00000000   Number         0  drv_uart.o ABSOLUTE
    ..\DriversMcu\STM32F4xx\Drivers\Drv_Usb.c 0x00000000   Number         0  drv_usb.o ABSOLUTE
    ..\DriversMcu\STM32F4xx\Drivers\Drv_led.c 0x00000000   Number         0  drv_led.o ABSOLUTE
    ..\DriversMcu\STM32F4xx\Drivers\stm32f4xx_it.c 0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    ..\DriversMcu\STM32F4xx\Libraries\CMSIS\ST\STM32F4xx\Source\Templates\arm\startup_stm32f429_439xx.s 0x00000000   Number         0  startup_stm32f429_439xx.o ABSOLUTE
    ..\DriversMcu\STM32F4xx\Libraries\CMSIS\ST\STM32F4xx\Source\Templates\system_stm32f4xx.c 0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    ..\DriversMcu\STM32F4xx\Libraries\STM32F4xx_StdPeriph_Driver\src\misc.c 0x00000000   Number         0  misc.o ABSOLUTE
    ..\DriversMcu\STM32F4xx\Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_adc.c 0x00000000   Number         0  stm32f4xx_adc.o ABSOLUTE
    ..\DriversMcu\STM32F4xx\Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_dma.c 0x00000000   Number         0  stm32f4xx_dma.o ABSOLUTE
    ..\DriversMcu\STM32F4xx\Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_exti.c 0x00000000   Number         0  stm32f4xx_exti.o ABSOLUTE
    ..\DriversMcu\STM32F4xx\Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_flash.c 0x00000000   Number         0  stm32f4xx_flash.o ABSOLUTE
    ..\DriversMcu\STM32F4xx\Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_gpio.c 0x00000000   Number         0  stm32f4xx_gpio.o ABSOLUTE
    ..\DriversMcu\STM32F4xx\Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_rcc.c 0x00000000   Number         0  stm32f4xx_rcc.o ABSOLUTE
    ..\DriversMcu\STM32F4xx\Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_spi.c 0x00000000   Number         0  stm32f4xx_spi.o ABSOLUTE
    ..\DriversMcu\STM32F4xx\Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_tim.c 0x00000000   Number         0  stm32f4xx_tim.o ABSOLUTE
    ..\DriversMcu\STM32F4xx\Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_usart.c 0x00000000   Number         0  stm32f4xx_usart.o ABSOLUTE
    ..\DriversMcu\STM32F4xx\cherryusb\usb_dc_dwc2.c 0x00000000   Number         0  usb_dc_dwc2.o ABSOLUTE
    ..\DriversMcu\STM32F4xx\cherryusb\usbd_cdc.c 0x00000000   Number         0  usbd_cdc.o ABSOLUTE
    ..\DriversMcu\STM32F4xx\cherryusb\usbd_core.c 0x00000000   Number         0  usbd_core.o ABSOLUTE
    ..\FcSrc\AnoPTv8\AnoPTv8Cmd.c            0x00000000   Number         0  anoptv8cmd.o ABSOLUTE
    ..\FcSrc\AnoPTv8\AnoPTv8ExAPI.c          0x00000000   Number         0  anoptv8exapi.o ABSOLUTE
    ..\FcSrc\AnoPTv8\AnoPTv8FrameFactory.c   0x00000000   Number         0  anoptv8framefactory.o ABSOLUTE
    ..\FcSrc\AnoPTv8\AnoPTv8Par.c            0x00000000   Number         0  anoptv8par.o ABSOLUTE
    ..\FcSrc\AnoPTv8\AnoPTv8Run.c            0x00000000   Number         0  anoptv8run.o ABSOLUTE
    ..\FcSrc\Ano_Scheduler.c                 0x00000000   Number         0  ano_scheduler.o ABSOLUTE
    ..\FcSrc\DataTransfer.c                  0x00000000   Number         0  datatransfer.o ABSOLUTE
    ..\FcSrc\LX_ExtSensor.c                  0x00000000   Number         0  lx_extsensor.o ABSOLUTE
    ..\FcSrc\LX_FcFunc.c                     0x00000000   Number         0  lx_fcfunc.o ABSOLUTE
    ..\FcSrc\LX_FcState.c                    0x00000000   Number         0  lx_fcstate.o ABSOLUTE
    ..\FcSrc\LX_LowLevelFunc.c               0x00000000   Number         0  lx_lowlevelfunc.o ABSOLUTE
    ..\FcSrc\User\Maixcam.c                  0x00000000   Number         0  maixcam.o ABSOLUTE
    ..\FcSrc\User\PID.c                      0x00000000   Number         0  pid.o ABSOLUTE
    ..\FcSrc\User\Tofsense-m.c               0x00000000   Number         0  tofsense-m.o ABSOLUTE
    ..\FcSrc\User\crc.c                      0x00000000   Number         0  crc.o ABSOLUTE
    ..\FcSrc\User\g_port.c                   0x00000000   Number         0  g_port.o ABSOLUTE
    ..\FcSrc\User\mid360.c                   0x00000000   Number         0  mid360.o ABSOLUTE
    ..\FcSrc\User\path_storage.c             0x00000000   Number         0  path_storage.o ABSOLUTE
    ..\FcSrc\User\tofmini.c                  0x00000000   Number         0  tofmini.o ABSOLUTE
    ..\FcSrc\User\zigbee.c                   0x00000000   Number         0  zigbee.o ABSOLUTE
    ..\FcSrc\User_Task.c                     0x00000000   Number         0  user_task.o ABSOLUTE
    ..\FcSrc\main.c                          0x00000000   Number         0  main.o ABSOLUTE
    ..\\DriversBsp\\Ano_Math.c               0x00000000   Number         0  ano_math.o ABSOLUTE
    ..\\DriversBsp\\DrvAnoOF_ptv7.c          0x00000000   Number         0  drvanoof_ptv7.o ABSOLUTE
    ..\\DriversBsp\\Drv_AnoOf.c              0x00000000   Number         0  drv_anoof.o ABSOLUTE
    ..\\DriversBsp\\Drv_BSP.c                0x00000000   Number         0  drv_bsp.o ABSOLUTE
    ..\\DriversBsp\\Drv_UbloxGPS.c           0x00000000   Number         0  drv_ubloxgps.o ABSOLUTE
    ..\\DriversMcu\\STM32F4xx\\Drivers\\Drv_Adc.c 0x00000000   Number         0  drv_adc.o ABSOLUTE
    ..\\DriversMcu\\STM32F4xx\\Drivers\\Drv_Dshot600.c 0x00000000   Number         0  drv_dshot600.o ABSOLUTE
    ..\\DriversMcu\\STM32F4xx\\Drivers\\Drv_PwmOut.c 0x00000000   Number         0  drv_pwmout.o ABSOLUTE
    ..\\DriversMcu\\STM32F4xx\\Drivers\\Drv_RcIn.c 0x00000000   Number         0  drv_rcin.o ABSOLUTE
    ..\\DriversMcu\\STM32F4xx\\Drivers\\Drv_Sys.c 0x00000000   Number         0  drv_sys.o ABSOLUTE
    ..\\DriversMcu\\STM32F4xx\\Drivers\\Drv_Timer.c 0x00000000   Number         0  drv_timer.o ABSOLUTE
    ..\\DriversMcu\\STM32F4xx\\Drivers\\Drv_Uart.c 0x00000000   Number         0  drv_uart.o ABSOLUTE
    ..\\DriversMcu\\STM32F4xx\\Drivers\\Drv_Usb.c 0x00000000   Number         0  drv_usb.o ABSOLUTE
    ..\\DriversMcu\\STM32F4xx\\Drivers\\Drv_led.c 0x00000000   Number         0  drv_led.o ABSOLUTE
    ..\\DriversMcu\\STM32F4xx\\Drivers\\stm32f4xx_it.c 0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    ..\\DriversMcu\\STM32F4xx\\Libraries\\CMSIS\\ST\\STM32F4xx\\Source\\Templates\\system_stm32f4xx.c 0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    ..\\DriversMcu\\STM32F4xx\\Libraries\\STM32F4xx_StdPeriph_Driver\\src\\misc.c 0x00000000   Number         0  misc.o ABSOLUTE
    ..\\DriversMcu\\STM32F4xx\\Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_adc.c 0x00000000   Number         0  stm32f4xx_adc.o ABSOLUTE
    ..\\DriversMcu\\STM32F4xx\\Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_dma.c 0x00000000   Number         0  stm32f4xx_dma.o ABSOLUTE
    ..\\DriversMcu\\STM32F4xx\\Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_exti.c 0x00000000   Number         0  stm32f4xx_exti.o ABSOLUTE
    ..\\DriversMcu\\STM32F4xx\\Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_flash.c 0x00000000   Number         0  stm32f4xx_flash.o ABSOLUTE
    ..\\DriversMcu\\STM32F4xx\\Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_gpio.c 0x00000000   Number         0  stm32f4xx_gpio.o ABSOLUTE
    ..\\DriversMcu\\STM32F4xx\\Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_rcc.c 0x00000000   Number         0  stm32f4xx_rcc.o ABSOLUTE
    ..\\DriversMcu\\STM32F4xx\\Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_spi.c 0x00000000   Number         0  stm32f4xx_spi.o ABSOLUTE
    ..\\DriversMcu\\STM32F4xx\\Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_tim.c 0x00000000   Number         0  stm32f4xx_tim.o ABSOLUTE
    ..\\DriversMcu\\STM32F4xx\\Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_usart.c 0x00000000   Number         0  stm32f4xx_usart.o ABSOLUTE
    ..\\FcSrc\\AnoPTv8\\AnoPTv8ExAPI.c       0x00000000   Number         0  anoptv8exapi.o ABSOLUTE
    ..\\FcSrc\\Ano_Scheduler.c               0x00000000   Number         0  ano_scheduler.o ABSOLUTE
    ..\\FcSrc\\DataTransfer.c                0x00000000   Number         0  datatransfer.o ABSOLUTE
    ..\\FcSrc\\LX_ExtSensor.c                0x00000000   Number         0  lx_extsensor.o ABSOLUTE
    ..\\FcSrc\\LX_FcFunc.c                   0x00000000   Number         0  lx_fcfunc.o ABSOLUTE
    ..\\FcSrc\\LX_FcState.c                  0x00000000   Number         0  lx_fcstate.o ABSOLUTE
    ..\\FcSrc\\LX_LowLevelFunc.c             0x00000000   Number         0  lx_lowlevelfunc.o ABSOLUTE
    ..\\FcSrc\\User\\Maixcam.c               0x00000000   Number         0  maixcam.o ABSOLUTE
    ..\\FcSrc\\User\\PID.c                   0x00000000   Number         0  pid.o ABSOLUTE
    ..\\FcSrc\\User\\Tofsense-m.c            0x00000000   Number         0  tofsense-m.o ABSOLUTE
    ..\\FcSrc\\User\\crc.c                   0x00000000   Number         0  crc.o ABSOLUTE
    ..\\FcSrc\\User\\g_port.c                0x00000000   Number         0  g_port.o ABSOLUTE
    ..\\FcSrc\\User\\mid360.c                0x00000000   Number         0  mid360.o ABSOLUTE
    ..\\FcSrc\\User\\path_storage.c          0x00000000   Number         0  path_storage.o ABSOLUTE
    ..\\FcSrc\\User\\tofmini.c               0x00000000   Number         0  tofmini.o ABSOLUTE
    ..\\FcSrc\\User\\zigbee.c                0x00000000   Number         0  zigbee.o ABSOLUTE
    ..\\FcSrc\\User_Task.c                   0x00000000   Number         0  user_task.o ABSOLUTE
    ..\\FcSrc\\main.c                        0x00000000   Number         0  main.o ABSOLUTE
    cdcmple.s                                0x00000000   Number         0  cdcmple.o ABSOLUTE
    cdrcmple.s                               0x00000000   Number         0  cdrcmple.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    RESET                                    0x08000000   Section      428  startup_stm32f429_439xx.o(RESET)
    .ARM.Collect$$$$00000000                 0x080001ac   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x080001ac   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x080001b0   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x080001b4   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x080001b4   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x080001b4   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    .ARM.Collect$$$$0000000E                 0x080001bc   Section        4  entry12b.o(.ARM.Collect$$$$0000000E)
    .ARM.Collect$$$$0000000F                 0x080001c0   Section        0  entry10a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00000011                 0x080001c0   Section        0  entry11a.o(.ARM.Collect$$$$00000011)
    .ARM.Collect$$$$00002712                 0x080001c0   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    __lit__00000000                          0x080001c0   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .text                                    0x080001c4   Section        0  main.o(.text)
    .text                                    0x080001d8   Section        0  ano_scheduler.o(.text)
    Loop_1000Hz                              0x080001d9   Thumb Code     2  ano_scheduler.o(.text)
    Loop_500Hz                               0x080001db   Thumb Code     2  ano_scheduler.o(.text)
    Loop_200Hz                               0x080001dd   Thumb Code     2  ano_scheduler.o(.text)
    Loop_100Hz                               0x080001df   Thumb Code    20  ano_scheduler.o(.text)
    Loop_50Hz                                0x080001f3   Thumb Code     8  ano_scheduler.o(.text)
    Loop_20Hz                                0x080001fb   Thumb Code     2  ano_scheduler.o(.text)
    Loop_2Hz                                 0x080001fd   Thumb Code    54  ano_scheduler.o(.text)
    .text                                    0x080002d8   Section        0  user_task.o(.text)
    calculate_actual_work_positions          0x080003f3   Thumb Code   188  user_task.o(.text)
    convert_path_to_coords                   0x080004af   Thumb Code   244  user_task.o(.text)
    convert_return_path_to_coords            0x080005a3   Thumb Code   152  user_task.o(.text)
    calculate_positioning_offset             0x0800063b   Thumb Code   528  user_task.o(.text)
    enable_position_control                  0x0800084b   Thumb Code    68  user_task.o(.text)
    execute_auto_positioning                 0x0800088f   Thumb Code   188  user_task.o(.text)
    set_target_position                      0x0800094b   Thumb Code    14  user_task.o(.text)
    execute_return_path_navigation           0x08000959   Thumb Code   298  user_task.o(.text)
    process_next_animal_target               0x08000a83   Thumb Code    58  user_task.o(.text)
    clear_animal_queue                       0x08000abd   Thumb Code    34  user_task.o(.text)
    add_animal_to_queue                      0x08000adf   Thumb Code    68  user_task.o(.text)
    trigger_animal_positioning               0x08000b23   Thumb Code    80  user_task.o(.text)
    reset_landing_context                    0x08000b73   Thumb Code    12  user_task.o(.text)
    execute_landing_sequence_v2              0x08000b7f   Thumb Code    74  user_task.o(.text)
    handle_landing_command                   0x08000bc9   Thumb Code    98  user_task.o(.text)
    handle_mission_command                   0x08000c2b   Thumb Code    48  user_task.o(.text)
    control_45deg_descent                    0x08000e7f   Thumb Code   634  user_task.o(.text)
    start_45deg_descent                      0x080010f9   Thumb Code    96  user_task.o(.text)
    handle_return_home                       0x08001159   Thumb Code    36  user_task.o(.text)
    handle_auto_positioning_state            0x0800117d   Thumb Code   550  user_task.o(.text)
    reset_specific_animal_counter            0x080013d9   Thumb Code    72  user_task.o(.text)
    process_animal_detection_simplified      0x080014c5   Thumb Code   220  user_task.o(.text)
    reset_animal_detection_counters          0x080015a1   Thumb Code    20  user_task.o(.text)
    handle_home_position_setup               0x080015b5   Thumb Code   116  user_task.o(.text)
    handle_mission_init                      0x08001629   Thumb Code   724  user_task.o(.text)
    execute_mission_state_machine            0x080018fd   Thumb Code  3592  user_task.o(.text)
    execute_mission_sequence                 0x08002705   Thumb Code    66  user_task.o(.text)
    process_animal_detection                 0x08002b13   Thumb Code   252  user_task.o(.text)
    is_landing_command                       0x08002c0f   Thumb Code    22  user_task.o(.text)
    is_mission_command                       0x08002c25   Thumb Code    24  user_task.o(.text)
    .text                                    0x08002c78   Section        0  datatransfer.o(.text)
    .text                                    0x080030b4   Section        0  lx_extsensor.o(.text)
    .text                                    0x08003128   Section        0  lx_fcfunc.o(.text)
    .text                                    0x0800333c   Section        0  lx_fcstate.o(.text)
    LX_Unlock_Lock_Check                     0x0800333d   Thumb Code   438  lx_fcstate.o(.text)
    .text                                    0x080035c0   Section        0  lx_lowlevelfunc.o(.text)
    RC_Data_Task                             0x080035c1   Thumb Code   582  lx_lowlevelfunc.o(.text)
    ESC_Output                               0x08003807   Thumb Code   350  lx_lowlevelfunc.o(.text)
    .text                                    0x08003a48   Section        0  crc.o(.text)
    .text                                    0x08003a80   Section        0  g_port.o(.text)
    .text                                    0x080040a4   Section        0  mid360.o(.text)
    .text                                    0x08004290   Section        0  pid.o(.text)
    get_cached_trig                          0x08005679   Thumb Code   130  pid.o(.text)
    .text                                    0x08005774   Section        0  zigbee.o(.text)
    .text                                    0x080066e0   Section        0  maixcam.o(.text)
    .text                                    0x0800684c   Section        0  tofmini.o(.text)
    .text                                    0x08006c14   Section        0  path_storage.o(.text)
    .text                                    0x08006e48   Section        0  anoptv8exapi.o(.text)
    .text                                    0x08007d38   Section        0  anoptv8run.o(.text)
    anoPTv8FrameCheck                        0x08007f2f   Thumb Code    68  anoptv8run.o(.text)
    anoPTv8FrameAnl                          0x08007f73   Thumb Code   110  anoptv8run.o(.text)
    .text                                    0x080080e8   Section        0  anoptv8cmd.o(.text)
    .text                                    0x080084c4   Section        0  anoptv8par.o(.text)
    .text                                    0x08008afc   Section        0  anoptv8framefactory.o(.text)
    .text                                    0x0800996c   Section        0  drv_bsp.o(.text)
    rcSignalCheck                            0x08009c29   Thumb Code   188  drv_bsp.o(.text)
    .text                                    0x08009e84   Section        0  ano_math.o(.text)
    .text                                    0x0800a828   Section        0  drv_anoof.o(.text)
    .text                                    0x0800aa20   Section        0  stm32f4xx_it.o(.text)
    .text                                    0x0800aaac   Section        0  drv_sys.o(.text)
    .text                                    0x0800ac14   Section        0  drv_led.o(.text)
    .text                                    0x0800ac58   Section        0  drv_timer.o(.text)
    .text                                    0x0800ace4   Section        0  drv_uart.o(.text)
    .text                                    0x0800bc0c   Section        0  drv_pwmout.o(.text)
    .text                                    0x0800bf7c   Section        0  drv_rcin.o(.text)
    crsf_crc8                                0x0800c14f   Thumb Code    58  drv_rcin.o(.text)
    .text                                    0x0800c33c   Section        0  drv_usb.o(.text)
    .text                                    0x0800c5d8   Section        0  drv_adc.o(.text)
    .text                                    0x0800c79c   Section        0  system_stm32f4xx.o(.text)
    SetSysClock                              0x0800c79d   Thumb Code   272  system_stm32f4xx.o(.text)
    .text                                    0x0800c9e0   Section        0  misc.o(.text)
    .text                                    0x0800cba0   Section        0  stm32f4xx_adc.o(.text)
    .text                                    0x0800db7c   Section        0  stm32f4xx_dma.o(.text)
    .text                                    0x0800eee4   Section        0  stm32f4xx_exti.o(.text)
    .text                                    0x0800f1b8   Section        0  stm32f4xx_gpio.o(.text)
    .text                                    0x0800fb90   Section        0  stm32f4xx_rcc.o(.text)
    .text                                    0x08010a80   Section        0  stm32f4xx_tim.o(.text)
    TI4_Config                               0x080126f9   Thumb Code    80  stm32f4xx_tim.o(.text)
    TI3_Config                               0x080127dd   Thumb Code    72  stm32f4xx_tim.o(.text)
    TI2_Config                               0x0801289b   Thumb Code    80  stm32f4xx_tim.o(.text)
    TI1_Config                               0x08012971   Thumb Code    58  stm32f4xx_tim.o(.text)
    .text                                    0x08013c6c   Section        0  stm32f4xx_usart.o(.text)
    .text                                    0x08014d50   Section       36  startup_stm32f429_439xx.o(.text)
    $v0                                      0x08014d50   Number         0  startup_stm32f429_439xx.o(.text)
    .text                                    0x08014d74   Section        0  usb_dc_dwc2.o(.text)
    dwc2_flush_rxfifo                        0x08014d75   Thumb Code    52  usb_dc_dwc2.o(.text)
    dwc2_flush_txfifo                        0x08014da9   Thumb Code    58  usb_dc_dwc2.o(.text)
    dwc2_set_turnaroundtime                  0x08014de3   Thumb Code   202  usb_dc_dwc2.o(.text)
    dwc2_set_txfifo                          0x08014ead   Thumb Code    72  usb_dc_dwc2.o(.text)
    dwc2_get_devspeed                        0x08014ef5   Thumb Code    30  usb_dc_dwc2.o(.text)
    dwc2_ep0_start_read_setup                0x08014f13   Thumb Code    38  usb_dc_dwc2.o(.text)
    dwc2_tx_fifo_empty_procecss              0x08014f77   Thumb Code   296  usb_dc_dwc2.o(.text)
    dwc2_core_init                           0x08015eed   Thumb Code   136  usb_dc_dwc2.o(.text)
    .text                                    0x08015f7c   Section        0  usbd_cdc.o(.text)
    cdc_acm_class_interface_request_handler  0x08015f93   Thumb Code   152  usbd_cdc.o(.text)
    .text                                    0x08016040   Section        0  usbd_core.o(.text)
    usbd_print_setup                         0x08016041   Thumb Code     6  usbd_core.o(.text)
    is_device_configured                     0x08016047   Thumb Code    16  usbd_core.o(.text)
    usbd_set_endpoint                        0x08016057   Thumb Code    50  usbd_core.o(.text)
    usbd_reset_endpoint                      0x08016089   Thumb Code    52  usbd_core.o(.text)
    usbd_get_descriptor                      0x080160bd   Thumb Code   238  usbd_core.o(.text)
    usbd_set_configuration                   0x080161ab   Thumb Code    98  usbd_core.o(.text)
    usbd_class_event_notify_handler          0x0801620d   Thumb Code    38  usbd_core.o(.text)
    usbd_set_interface                       0x08016233   Thumb Code   118  usbd_core.o(.text)
    usbd_std_device_req_handler              0x080162db   Thumb Code   204  usbd_core.o(.text)
    usbd_std_interface_req_handler           0x080163a7   Thumb Code   200  usbd_core.o(.text)
    usbd_std_endpoint_req_handler            0x0801646f   Thumb Code   132  usbd_core.o(.text)
    usbd_standard_request_handler            0x080164f3   Thumb Code    98  usbd_core.o(.text)
    usbd_class_request_handler               0x08016555   Thumb Code   108  usbd_core.o(.text)
    usbd_vendor_request_handler              0x080165c1   Thumb Code   232  usbd_core.o(.text)
    usbd_setup_request_handler               0x080166a9   Thumb Code   148  usbd_core.o(.text)
    .text                                    0x08016aa8   Section        0  memcpya.o(.text)
    .text                                    0x08016acc   Section        0  memseta.o(.text)
    .text                                    0x08016af0   Section        0  strcat.o(.text)
    .text                                    0x08016b08   Section        0  memcmp.o(.text)
    .text                                    0x08016b22   Section        0  dadd.o(.text)
    .text                                    0x08016c70   Section        0  dmul.o(.text)
    .text                                    0x08016d54   Section        0  ffltl.o(.text)
    .text                                    0x08016d80   Section        0  dflti.o(.text)
    .text                                    0x08016da2   Section        0  dfltl.o(.text)
    .text                                    0x08016dca   Section        0  f2d.o(.text)
    .text                                    0x08016df0   Section       48  cdcmple.o(.text)
    .text                                    0x08016e20   Section       48  cdrcmple.o(.text)
    .text                                    0x08016e50   Section        0  d2f.o(.text)
    .text                                    0x08016e88   Section        0  uidiv.o(.text)
    .text                                    0x08016eb4   Section        0  uldiv.o(.text)
    .text                                    0x08016f16   Section        0  llshl.o(.text)
    .text                                    0x08016f34   Section        0  llsshr.o(.text)
    .text                                    0x08016f58   Section        0  iusefp.o(.text)
    .text                                    0x08016f58   Section        0  fepilogue.o(.text)
    .text                                    0x08016fc6   Section        0  depilogue.o(.text)
    .text                                    0x08017080   Section        0  ddiv.o(.text)
    .text                                    0x0801715e   Section        0  dfixul.o(.text)
    .text                                    0x08017190   Section       36  init.o(.text)
    .text                                    0x080171b4   Section        0  llushr.o(.text)
    .text                                    0x080171d4   Section        0  __dczerorl2.o(.text)
    i.__0sprintf                             0x0801722c   Section        0  printfa.o(i.__0sprintf)
    i.__ARM_fpclassifyf                      0x08017254   Section        0  fpclassifyf.o(i.__ARM_fpclassifyf)
    i.__hardfp_atan2f                        0x0801727c   Section        0  atan2f.o(i.__hardfp_atan2f)
    i.__hardfp_sqrtf                         0x08017528   Section        0  sqrtf.o(i.__hardfp_sqrtf)
    i.__mathlib_flt_infnan2                  0x08017562   Section        0  funder.o(i.__mathlib_flt_infnan2)
    i.__mathlib_flt_underflow                0x08017568   Section        0  funder.o(i.__mathlib_flt_underflow)
    i.__scatterload_copy                     0x08017578   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x08017586   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x08017588   Section       14  handlers.o(i.__scatterload_zeroinit)
    i.__set_errno                            0x08017598   Section        0  errno.o(i.__set_errno)
    i._fp_digits                             0x080175a4   Section        0  printfa.o(i._fp_digits)
    _fp_digits                               0x080175a5   Thumb Code   366  printfa.o(i._fp_digits)
    i._printf_core                           0x08017728   Section        0  printfa.o(i._printf_core)
    _printf_core                             0x08017729   Thumb Code  1704  printfa.o(i._printf_core)
    i._printf_post_padding                   0x08017ddc   Section        0  printfa.o(i._printf_post_padding)
    _printf_post_padding                     0x08017ddd   Thumb Code    36  printfa.o(i._printf_post_padding)
    i._printf_pre_padding                    0x08017e00   Section        0  printfa.o(i._printf_pre_padding)
    _printf_pre_padding                      0x08017e01   Thumb Code    46  printfa.o(i._printf_pre_padding)
    i._sputc                                 0x08017e2e   Section        0  printfa.o(i._sputc)
    _sputc                                   0x08017e2f   Thumb Code    10  printfa.o(i._sputc)
    i.is_z_position_reached                  0x08017e38   Section        0  user_task.o(i.is_z_position_reached)
    .constdata                               0x08017e74   Section       26  datatransfer.o(.constdata)
    .constdata                               0x08017e8e   Section     9200  path_storage.o(.constdata)
    path_lookup_table                        0x08017e8e   Data        9200  path_storage.o(.constdata)
    .constdata                               0x0801a27e   Section     1773  anoptv8exapi.o(.constdata)
    .constdata                               0x0801a96c   Section     1028  ano_math.o(.constdata)
    .constdata                               0x0801ad70   Section      148  drv_usb.o(.constdata)
    cdc_descriptor                           0x0801ad70   Data         148  drv_usb.o(.constdata)
    .conststring                             0x0801ae04   Section      434  user_task.o(.conststring)
    .conststring                             0x0801afb8   Section      140  path_storage.o(.conststring)
    .conststring                             0x0801b044   Section       72  misc.o(.conststring)
    .conststring                             0x0801b08c   Section       81  stm32f4xx_adc.o(.conststring)
    .conststring                             0x0801b0e0   Section       81  stm32f4xx_dma.o(.conststring)
    .conststring                             0x0801b134   Section       82  stm32f4xx_exti.o(.conststring)
    .conststring                             0x0801b188   Section       82  stm32f4xx_gpio.o(.conststring)
    .conststring                             0x0801b1dc   Section       81  stm32f4xx_rcc.o(.conststring)
    .conststring                             0x0801b230   Section       81  stm32f4xx_tim.o(.conststring)
    .conststring                             0x0801b284   Section       83  stm32f4xx_usart.o(.conststring)
    .data                                    0x20000000   Section       85  ano_scheduler.o(.data)
    sched_tasks                              0x20000000   Data          84  ano_scheduler.o(.data)
    _ledflag                                 0x20000054   Data           1  ano_scheduler.o(.data)
    .data                                    0x20000058   Section      892  user_task.o(.data)
    led_timer_ms                             0x20000058   Data           2  user_task.o(.data)
    dadian_f                                 0x2000005d   Data           1  user_task.o(.data)
    patrol_points_completed                  0x20000064   Data           4  user_task.o(.data)
    sent_count                               0x2000006c   Data           4  user_task.o(.data)
    sent_pairs_count                         0x20000070   Data           4  user_task.o(.data)
    patrol_state                             0x20000074   Data           1  user_task.o(.data)
    current_return_path_length               0x20000078   Data           4  user_task.o(.data)
    current_return_step                      0x2000007c   Data           4  user_task.o(.data)
    return_path_loaded                       0x20000080   Data           1  user_task.o(.data)
    descent_start_height                     0x20000084   Data           4  user_task.o(.data)
    descent_distance                         0x20000088   Data           4  user_task.o(.data)
    descent_angle                            0x2000008c   Data           4  user_task.o(.data)
    descent_speed                            0x20000090   Data           4  user_task.o(.data)
    g_landing_context                        0x20000094   Data           4  user_task.o(.data)
    current_path_ptr                         0x200000a0   Data           4  user_task.o(.data)
    precomputed_path_length                  0x200000a4   Data           4  user_task.o(.data)
    current_path_index                       0x200000a8   Data           4  user_task.o(.data)
    patrol_path_length                       0x200000ac   Data           4  user_task.o(.data)
    current_patrol_step                      0x200000b0   Data           4  user_task.o(.data)
    internal_patrol_step                     0x200000b4   Data           4  user_task.o(.data)
    patrol_initialized                       0x200000b8   Data           1  user_task.o(.data)
    animal_detection_counters                0x200000b9   Data           6  user_task.o(.data)
    last_position_code                       0x200000bf   Data           1  user_task.o(.data)
    auto_pos_state                           0x200000c0   Data           1  user_task.o(.data)
    auto_pos_timer_ms                        0x200000c2   Data           2  user_task.o(.data)
    current_processing_index                 0x200000c4   Data           1  user_task.o(.data)
    total_animals                            0x200000c5   Data           1  user_task.o(.data)
    multi_target_sequential_enabled          0x200000c6   Data           1  user_task.o(.data)
    mission_active                           0x200003be   Data           1  user_task.o(.data)
    mission_timer_ms                         0x200003c0   Data           2  user_task.o(.data)
    saved_patrol_state                       0x200003c2   Data           1  user_task.o(.data)
    saved_mission_timer_ms                   0x200003c4   Data           2  user_task.o(.data)
    patrol_context_saved                     0x200003c6   Data           1  user_task.o(.data)
    need_visual_correction                   0x200003c7   Data           1  user_task.o(.data)
    vision_correction_timeout_ms             0x200003c8   Data           2  user_task.o(.data)
    descent_initialized                      0x200003ca   Data           1  user_task.o(.data)
    last_reminder_time                       0x200003cc   Data           4  user_task.o(.data)
    last_trajectory_debug                    0x200003d0   Data           4  user_task.o(.data)
    .data                                    0x200003d4   Section        1  datatransfer.o(.data)
    last_idx                                 0x200003d4   Data           1  datatransfer.o(.data)
    .data                                    0x200003d5   Section        1  lx_fcfunc.o(.data)
    old_mode                                 0x200003d5   Data           1  lx_fcfunc.o(.data)
    .data                                    0x200003d6   Section        6  lx_fcstate.o(.data)
    time_dly_cnt_ms                          0x200003d8   Data           2  lx_fcstate.o(.data)
    unlock_lock_flag                         0x200003da   Data           1  lx_fcstate.o(.data)
    cali_f                                   0x200003db   Data           1  lx_fcstate.o(.data)
    .data                                    0x200003dc   Section       28  lx_lowlevelfunc.o(.data)
    fail_safe_change_mod                     0x200003ed   Data           1  lx_lowlevelfunc.o(.data)
    fail_safe_return_home                    0x200003ee   Data           1  lx_lowlevelfunc.o(.data)
    mod_f                                    0x200003ef   Data           3  lx_lowlevelfunc.o(.data)
    mod_f_time_cnt                           0x200003f2   Data           2  lx_lowlevelfunc.o(.data)
    tmp_cnt                                  0x200003f4   Data           4  lx_lowlevelfunc.o(.data)
    .data                                    0x200003f8   Section     1024  crc.o(.data)
    .data                                    0x200007f8   Section        9  g_port.o(.data)
    parse_state                              0x200007f8   Data           1  g_port.o(.data)
    data_length                              0x200007f9   Data           1  g_port.o(.data)
    cmd                                      0x200007fa   Data           1  g_port.o(.data)
    header_checksum                          0x200007fb   Data           1  g_port.o(.data)
    received_crc32                           0x200007fc   Data           4  g_port.o(.data)
    crc32_count                              0x20000800   Data           1  g_port.o(.data)
    .data                                    0x20000802   Section        5  mid360.o(.data)
    check_time_ms                            0x20000802   Data           2  mid360.o(.data)
    data_valid_flag                          0x20000804   Data           1  mid360.o(.data)
    my_flag                                  0x20000805   Data           1  mid360.o(.data)
    _datalen                                 0x20000806   Data           1  mid360.o(.data)
    .data                                    0x20000808   Section      216  pid.o(.data)
    yaw_cache                                0x200008c4   Data          16  pid.o(.data)
    data_loss_counter                        0x200008d4   Data           2  pid.o(.data)
    filter_alpha_x                           0x200008d8   Data           4  pid.o(.data)
    filter_alpha_y                           0x200008dc   Data           4  pid.o(.data)
    .data                                    0x200008e0   Section       39  zigbee.o(.data)
    coordinate_scale_x                       0x200008e4   Data           4  zigbee.o(.data)
    coordinate_scale_y                       0x200008e8   Data           4  zigbee.o(.data)
    coordinate_offset_x                      0x200008ec   Data           2  zigbee.o(.data)
    coordinate_offset_y                      0x200008ee   Data           2  zigbee.o(.data)
    calibration_applied                      0x200008f0   Data           1  zigbee.o(.data)
    radar_coord_send_enabled                 0x200008f1   Data           1  zigbee.o(.data)
    calib_point1_mid360_x                    0x200008f2   Data           2  zigbee.o(.data)
    calib_point1_mid360_y                    0x200008f4   Data           2  zigbee.o(.data)
    calib_point1_actual_x                    0x200008f6   Data           2  zigbee.o(.data)
    calib_point1_actual_y                    0x200008f8   Data           2  zigbee.o(.data)
    calib_point2_actual_x                    0x200008fa   Data           2  zigbee.o(.data)
    calib_point2_actual_y                    0x200008fc   Data           2  zigbee.o(.data)
    calib_point1_set                         0x200008fe   Data           1  zigbee.o(.data)
    g_no_fly_zones                           0x200008ff   Data           3  zigbee.o(.data)
    g_no_fly_zone_count                      0x20000902   Data           1  zigbee.o(.data)
    screen_flag                              0x20000903   Data           1  zigbee.o(.data)
    screen_datalen                           0x20000904   Data           1  zigbee.o(.data)
    screen_cmd                               0x20000905   Data           1  zigbee.o(.data)
    screen_expected_len                      0x20000906   Data           1  zigbee.o(.data)
    .data                                    0x20000907   Section        8  maixcam.o(.data)
    maixcam_flag                             0x20000907   Data           1  maixcam.o(.data)
    maixcam_datalen                          0x20000908   Data           1  maixcam.o(.data)
    maixcam_receive_buf                      0x20000909   Data           6  maixcam.o(.data)
    .data                                    0x20000910   Section       36  anoptv8exapi.o(.data)
    .data                                    0x20000934   Section        2  anoptv8run.o(.data)
    lastSequence                             0x20000934   Data           2  anoptv8run.o(.data)
    .data                                    0x20000938   Section       10  anoptv8cmd.o(.data)
    cmdSendStatus                            0x2000093c   Data           1  anoptv8cmd.o(.data)
    cmdInfoNeedToCheck                       0x2000093d   Data           3  anoptv8cmd.o(.data)
    cmdSendOverTimeCnt                       0x20000940   Data           2  anoptv8cmd.o(.data)
    .data                                    0x20000944   Section        4  anoptv8par.o(.data)
    .data                                    0x20000948   Section       19  drv_bsp.o(.data)
    ch_sta                                   0x20000949   Data           1  drv_bsp.o(.data)
    sbus_time                                0x2000094c   Data           8  drv_bsp.o(.data)
    cnt                                      0x20000954   Data           1  drv_bsp.o(.data)
    frame_cnt                                0x20000955   Data           1  drv_bsp.o(.data)
    cnt_tmp                                  0x20000956   Data           1  drv_bsp.o(.data)
    time_dly                                 0x20000958   Data           2  drv_bsp.o(.data)
    failsafe                                 0x2000095a   Data           1  drv_bsp.o(.data)
    .data                                    0x20000960   Section        8  drv_sys.o(.data)
    SysRunTimeMs                             0x20000960   Data           8  drv_sys.o(.data)
    .data                                    0x20000968   Section       56  drv_uart.o(.data)
    .data                                    0x200009a0   Section        5  drv_rcin.o(.data)
    temp_cnt                                 0x200009a0   Data           4  drv_rcin.o(.data)
    crsf_rx_index                            0x200009a4   Data           1  drv_rcin.o(.data)
    .data                                    0x200009a8   Section       25  drv_usb.o(.data)
    _cdcTxBustCnt                            0x200009c0   Data           1  drv_usb.o(.data)
    .data                                    0x200009c4   Section        4  drv_adc.o(.data)
    .data                                    0x200009c8   Section       20  system_stm32f4xx.o(.data)
    .data                                    0x200009dc   Section       16  stm32f4xx_rcc.o(.data)
    APBAHBPrescTable                         0x200009dc   Data          16  stm32f4xx_rcc.o(.data)
    .data                                    0x200009ec   Section       16  usbd_core.o(.data)
    msosv1_desc                              0x200009f0   Data           4  usbd_core.o(.data)
    msosv2_desc                              0x200009f4   Data           4  usbd_core.o(.data)
    bos_desc                                 0x200009f8   Data           4  usbd_core.o(.data)
    .data                                    0x200009fc   Section        4  errno.o(.data)
    _errno                                   0x200009fc   Data           4  errno.o(.data)
    .noncacheable                            0x20000a00   Section      256  drv_usb.o(.noncacheable)
    .noncacheable                            0x20000b00   Section      192  usb_dc_dwc2.o(.noncacheable)
    .noncacheable                            0x20000bc0   Section      288  usbd_core.o(.noncacheable)
    .bss                                     0x20000ce0   Section     1962  user_task.o(.bss)
    patrol_point_status                      0x20000ce0   Data          63  user_task.o(.bss)
    sent_position_codes                      0x20000d1f   Data          70  user_task.o(.bss)
    sent_pairs                               0x20000d65   Data         630  user_task.o(.bss)
    current_return_path_data                 0x20000fdb   Data          25  user_task.o(.bss)
    precomputed_path_buffer                  0x20000ff4   Data          70  user_task.o(.bss)
    patrol_path_coords                       0x2000103a   Data         560  user_task.o(.bss)
    animal_queue                             0x2000126a   Data          40  user_task.o(.bss)
    .bss                                     0x2000148a   Section       24  datatransfer.o(.bss)
    ASFSta                                   0x2000148a   Data          24  datatransfer.o(.bss)
    .bss                                     0x200014a2   Section       48  lx_extsensor.o(.bss)
    .bss                                     0x200014d2   Section       11  lx_fcstate.o(.bss)
    .bss                                     0x200014de   Section       64  lx_lowlevelfunc.o(.bss)
    pwm                                      0x2000150e   Data          16  lx_lowlevelfunc.o(.bss)
    .bss                                     0x2000151e   Section      112  g_port.o(.bss)
    control_data                             0x20001536   Data          14  g_port.o(.bss)
    control_data                             0x20001544   Data          14  g_port.o(.bss)
    control_data                             0x20001552   Data          14  g_port.o(.bss)
    control_data                             0x20001560   Data          14  g_port.o(.bss)
    data_buffer                              0x2000156e   Data          32  g_port.o(.bss)
    .bss                                     0x20001590   Section      108  mid360.o(.bss)
    mid360_data_old                          0x200015d8   Data          12  mid360.o(.bss)
    debug_info                               0x200015e4   Data          24  mid360.o(.bss)
    .bss                                     0x200015fc   Section      720  pid.o(.bss)
    .bss                                     0x200018cc   Section       64  zigbee.o(.bss)
    screen_receive_buf                       0x200018cc   Data          64  zigbee.o(.bss)
    .bss                                     0x2000190c   Section       13  maixcam.o(.bss)
    .bss                                     0x2000191c   Section       76  tofmini.o(.bss)
    g_tfmini_parser                          0x2000191c   Data          32  tofmini.o(.bss)
    .bss                                     0x20001968   Section     6016  anoptv8run.o(.bss)
    .bss                                     0x200030e8   Section      512  anoptv8cmd.o(.bss)
    .bss                                     0x200032e8   Section     2048  anoptv8par.o(.bss)
    .bss                                     0x20003ae8   Section      166  drv_bsp.o(.bss)
    datatmp                                  0x20003b75   Data          25  drv_bsp.o(.bss)
    .bss                                     0x20003b90   Section       72  drv_anoof.o(.bss)
    check_time_ms                            0x20003bcc   Data          12  drv_anoof.o(.bss)
    .bss                                     0x20003bd8   Section     7168  drv_uart.o(.bss)
    .bss                                     0x200057d8   Section      136  drv_rcin.o(.bss)
    crsf_rx_buffer                           0x2000581c   Data          68  drv_rcin.o(.bss)
    .bss                                     0x20005860   Section     1088  drv_usb.o(.bss)
    .bss                                     0x20005ca0   Section       16  drv_adc.o(.bss)
    .bss                                     0x20005cb0   Section      192  usbd_core.o(.bss)
    STACK                                    0x20005d70   Section     1024  startup_stm32f429_439xx.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_a                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_c                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_charcount                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_d                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_e                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_f                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_dec                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_hex                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_g                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_i                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_int_dec                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_l                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ll                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lld                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lli                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llo                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llu                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llx                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_dec                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_hex                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_oct                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ls                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_mbtowc                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_n                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_o                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_p                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_percent                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_pre_padding                      0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_s                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_str                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_signed                  0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_unsigned                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_u                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wctomb                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_x                                0x00000000   Number         0  stubs.o ABSOLUTE
    __arm_fini_                               - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    __Vectors_Size                           0x000001ac   Number         0  startup_stm32f429_439xx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f429_439xx.o(RESET)
    __Vectors_End                            0x080001ac   Data           0  startup_stm32f429_439xx.o(RESET)
    __main                                   0x080001ad   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x080001ad   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x080001b1   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x080001b5   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x080001b5   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x080001b5   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x080001b5   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_lib_shutdown_fini                   0x080001bd   Thumb Code     0  entry12b.o(.ARM.Collect$$$$0000000E)
    __rt_final_cpp                           0x080001c1   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000F)
    __rt_final_exit                          0x080001c1   Thumb Code     0  entry11a.o(.ARM.Collect$$$$00000011)
    assert_failed                            0x080001c5   Thumb Code     4  main.o(.text)
    main                                     0x080001c9   Thumb Code    16  main.o(.text)
    Scheduler_Setup                          0x08000233   Thumb Code    76  ano_scheduler.o(.text)
    Scheduler_Run                            0x0800027f   Thumb Code    76  ano_scheduler.o(.text)
    LED_PWM_Control                          0x080002d9   Thumb Code    62  user_task.o(.text)
    jiguang                                  0x08000317   Thumb Code    66  user_task.o(.text)
    is_position_reached                      0x08000359   Thumb Code   100  user_task.o(.text)
    is_yaw_reached                           0x080003bd   Thumb Code    54  user_task.o(.text)
    get_patrol_statistics                    0x08000c5b   Thumb Code   522  user_task.o(.text)
    handle_wait                              0x08000e65   Thumb Code    26  user_task.o(.text)
    mark_patrol_point_completed              0x080013a3   Thumb Code    16  user_task.o(.text)
    find_work_pos_index_by_position_code     0x080013b3   Thumb Code    38  user_task.o(.text)
    is_position_animal_sent                  0x08001421   Thumb Code    58  user_task.o(.text)
    mark_position_animal_sent                0x0800145b   Thumb Code   106  user_task.o(.text)
    land                                     0x08002747   Thumb Code    50  user_task.o(.text)
    handle_work_point_navigation             0x08002779   Thumb Code    58  user_task.o(.text)
    UserTask_OneKeyCmd                       0x080027b3   Thumb Code    56  user_task.o(.text)
    is_patrol_point_completed                0x080027eb   Thumb Code    20  user_task.o(.text)
    is_position_code_sent                    0x080027ff   Thumb Code    32  user_task.o(.text)
    mark_position_code_sent                  0x0800281f   Thumb Code    38  user_task.o(.text)
    is_patrol_complete                       0x08002845   Thumb Code    56  user_task.o(.text)
    check_mission_timeout                    0x0800287d   Thumb Code   662  user_task.o(.text)
    AnoDTLxFrameSend                         0x08002c79   Thumb Code   558  datatransfer.o(.text)
    AutoSendFrameCheck                       0x08002ea7   Thumb Code   210  datatransfer.o(.text)
    AnoDTLxRunTask1Ms                        0x08002f79   Thumb Code    20  datatransfer.o(.text)
    AnoDTLxFrameAnl                          0x08002f8d   Thumb Code   190  datatransfer.o(.text)
    AnoDTLxFrameSendTrigger                  0x0800304b   Thumb Code   106  datatransfer.o(.text)
    LX_FC_EXT_Sensor_Task                    0x080030b5   Thumb Code   106  lx_extsensor.o(.text)
    FC_Unlock                                0x08003129   Thumb Code    38  lx_fcfunc.o(.text)
    FC_Lock                                  0x0800314f   Thumb Code    38  lx_fcfunc.o(.text)
    LX_Change_Mode                           0x08003175   Thumb Code    62  lx_fcfunc.o(.text)
    OneKey_Return_Home                       0x080031b3   Thumb Code    32  lx_fcfunc.o(.text)
    OneKey_Takeoff                           0x080031d3   Thumb Code    52  lx_fcfunc.o(.text)
    OneKey_Land                              0x08003207   Thumb Code    32  lx_fcfunc.o(.text)
    Horizontal_Move                          0x08003227   Thumb Code    96  lx_fcfunc.o(.text)
    Horizontal_Calibrate                     0x08003287   Thumb Code    32  lx_fcfunc.o(.text)
    Mag_Calibrate                            0x080032a7   Thumb Code    32  lx_fcfunc.o(.text)
    ACC_Calibrate                            0x080032c7   Thumb Code    32  lx_fcfunc.o(.text)
    GYR_Calibrate                            0x080032e7   Thumb Code    32  lx_fcfunc.o(.text)
    LX_Cali_Trig_Check                       0x080034f3   Thumb Code   150  lx_fcstate.o(.text)
    LX_FC_State_Task                         0x08003589   Thumb Code    26  lx_fcstate.o(.text)
    ANO_LX_Task                              0x08003965   Thumb Code   224  lx_lowlevelfunc.o(.text)
    crc_32                                   0x08003a49   Thumb Code    52  crc.o(.text)
    CalcHeaderChecksum                       0x08003a81   Thumb Code    10  g_port.o(.text)
    SendGimbalControlCmd                     0x08003a8b   Thumb Code   316  g_port.o(.text)
    GimbalSpeedControl                       0x08003bc7   Thumb Code   104  g_port.o(.text)
    GimbalAngleControl                       0x08003c2f   Thumb Code   104  g_port.o(.text)
    GimbalCenter                             0x08003c97   Thumb Code    16  g_port.o(.text)
    GimbalLock                               0x08003ca7   Thumb Code   104  g_port.o(.text)
    GimbalAngle_GetOneByte                   0x08003d0f   Thumb Code   536  g_port.o(.text)
    GetIMUAngle                              0x08003f27   Thumb Code    80  g_port.o(.text)
    GetHallAngle                             0x08003f77   Thumb Code   134  g_port.o(.text)
    GetAngleSpeed                            0x08003ffd   Thumb Code   164  g_port.o(.text)
    mid360_GetOneByte                        0x080040a5   Thumb Code   340  mid360.o(.text)
    mid360_check_state                       0x080041f9   Thumb Code    76  mid360.o(.text)
    mid360_is_data_valid                     0x08004245   Thumb Code    22  mid360.o(.text)
    mid360_get_debug_info                    0x0800425b   Thumb Code     4  mid360.o(.text)
    mid360_reset_debug_info                  0x0800425f   Thumb Code    16  mid360.o(.text)
    PID_Calc                                 0x08004291   Thumb Code   476  pid.o(.text)
    PID_Init                                 0x0800446d   Thumb Code  1234  pid.o(.text)
    OutLoop_Control_Z                        0x0800493f   Thumb Code    76  pid.o(.text)
    OutLoop_Control_XY                       0x0800498b   Thumb Code   330  pid.o(.text)
    OutLoop_Control_yaw                      0x08004ad5   Thumb Code   284  pid.o(.text)
    is_visual_data_valid                     0x08004bf1   Thumb Code    82  pid.o(.text)
    handle_visual_data_loss                  0x08004c43   Thumb Code    60  pid.o(.text)
    limit_visual_offset_values               0x08004c7f   Thumb Code   130  pid.o(.text)
    limit_target_position                    0x08004d01   Thumb Code   130  pid.o(.text)
    limit_visual_offset                      0x08004d83   Thumb Code    12  pid.o(.text)
    OutLoop_Control_cam                      0x08004d8f   Thumb Code   176  pid.o(.text)
    XY_flag_Control                          0x08004e3f   Thumb Code     8  pid.o(.text)
    Enhanced_Visual_Control                  0x08004e47   Thumb Code   260  pid.o(.text)
    visual_pid_flag_Control                  0x08004f4b   Thumb Code   126  pid.o(.text)
    OutLoop_Control_g_port                   0x08004fc9   Thumb Code  1150  pid.o(.text)
    YAW_flag_Control                         0x08005447   Thumb Code    20  pid.o(.text)
    Z_flag_Control                           0x0800545b   Thumb Code    20  pid.o(.text)
    cam_flag_Control                         0x0800546f   Thumb Code    20  pid.o(.text)
    g_port_flag_Control                      0x08005483   Thumb Code    20  pid.o(.text)
    laser_flag_Control                       0x08005497   Thumb Code    26  pid.o(.text)
    Laser_Set_Target_Pixel                   0x080054b1   Thumb Code    40  pid.o(.text)
    Laser_Set_Target_Center                  0x080054d9   Thumb Code    12  pid.o(.text)
    Laser_Get_Pixel_Error_X                  0x080054e5   Thumb Code     8  pid.o(.text)
    Laser_Get_Pixel_Error_Y                  0x080054ed   Thumb Code     8  pid.o(.text)
    Laser_Is_On_Target                       0x080054f5   Thumb Code    70  pid.o(.text)
    all_flag_reset                           0x0800553b   Thumb Code   318  pid.o(.text)
    zigbee_apply_coordinate_calibration      0x08005775   Thumb Code   312  zigbee.o(.text)
    zigbee_execute_two_point_calibration     0x080058ad   Thumb Code   418  zigbee.o(.text)
    zigbee_set_calibration_point_1           0x08005a4f   Thumb Code    56  zigbee.o(.text)
    zigbee_send_screen_data                  0x08005a87   Thumb Code    32  zigbee.o(.text)
    zigbee_validate_continuous_no_fly_zones  0x08005aa7   Thumb Code   826  zigbee.o(.text)
    path_planner_position_code_to_index      0x08005de1   Thumb Code    70  zigbee.o(.text)
    zigbee_process_no_fly_zones              0x08005e27   Thumb Code   468  zigbee.o(.text)
    zigbee_screen_data_handler               0x08005ffb   Thumb Code   316  zigbee.o(.text)
    screen_receiver_GetOneByte               0x08006137   Thumb Code  1060  zigbee.o(.text)
    zigbee_send_screen_data1                 0x0800655b   Thumb Code    32  zigbee.o(.text)
    zigbee_send_screen_animal                0x0800657b   Thumb Code    38  zigbee.o(.text)
    zigbee_send_radar_coordinates            0x080065a1   Thumb Code    62  zigbee.o(.text)
    zigbee_reset_coordinate_calibration      0x080065df   Thumb Code    54  zigbee.o(.text)
    zigbee_get_no_fly_zones                  0x08006615   Thumb Code    38  zigbee.o(.text)
    reset_patrol_order                       0x0800663b   Thumb Code    30  zigbee.o(.text)
    maixcam_receiver_GetOneByte              0x080066e1   Thumb Code   206  maixcam.o(.text)
    maixcam_send_data                        0x080067af   Thumb Code    86  maixcam.o(.text)
    maixcam_get_x                            0x08006805   Thumb Code     8  maixcam.o(.text)
    maixcam_get_y                            0x0800680d   Thumb Code     8  maixcam.o(.text)
    maixcam_get_count                        0x08006815   Thumb Code     6  maixcam.o(.text)
    maixcam_is_data_valid                    0x0800681b   Thumb Code     6  maixcam.o(.text)
    maixcam_get_id                           0x08006821   Thumb Code     6  maixcam.o(.text)
    maixcam_clear_data                       0x08006827   Thumb Code    20  maixcam.o(.text)
    tfmini_parser_init                       0x0800684d   Thumb Code    54  tofmini.o(.text)
    tofmini_init                             0x08006883   Thumb Code    74  tofmini.o(.text)
    tofmini_get_distance_cm                  0x080068cd   Thumb Code     6  tofmini.o(.text)
    tofmini_is_distance_valid                0x080068d3   Thumb Code     6  tofmini.o(.text)
    tofmini_check_state                      0x080068d9   Thumb Code    40  tofmini.o(.text)
    tfmini_temporal_filter                   0x08006901   Thumb Code   102  tofmini.o(.text)
    tfmini_validate_frame                    0x08006967   Thumb Code    74  tofmini.o(.text)
    tfmini_convert_distance_cm               0x080069b1   Thumb Code    56  tofmini.o(.text)
    tfmini_process_frame                     0x080069e9   Thumb Code    94  tofmini.o(.text)
    tfmini_calculate_checksum                0x08006a47   Thumb Code    64  tofmini.o(.text)
    tfmini_parse_byte                        0x08006a87   Thumb Code   248  tofmini.o(.text)
    TOFmini_RecvOneByte                      0x08006b7f   Thumb Code    26  tofmini.o(.text)
    tofmini_set_filter                       0x08006b99   Thumb Code    56  tofmini.o(.text)
    tofmini_get_sensor_info                  0x08006bd1   Thumb Code     4  tofmini.o(.text)
    tfmini_calculate_distance                0x08006bd5   Thumb Code     6  tofmini.o(.text)
    tfmini_convert_temperature_celsius       0x08006bdb   Thumb Code    20  tofmini.o(.text)
    tfmini_parser_reset                      0x08006bef   Thumb Code    28  tofmini.o(.text)
    find_precomputed_path                    0x08006c15   Thumb Code   152  path_storage.o(.text)
    find_precomputed_return_path             0x08006cad   Thumb Code   156  path_storage.o(.text)
    AnoPTv8HwSendBytes                       0x08006e49   Thumb Code   122  anoptv8exapi.o(.text)
    AnoPTv8HwTrigger1ms                      0x08006ec3   Thumb Code     8  anoptv8exapi.o(.text)
    AnoPTv8HwRecvByte                        0x08006ecb   Thumb Code    16  anoptv8exapi.o(.text)
    AnoPTv8HwParValRecvCallback              0x08006edb   Thumb Code     2  anoptv8exapi.o(.text)
    AnoPTv8HwParCmdRecvCallback              0x08006edd   Thumb Code     2  anoptv8exapi.o(.text)
    AnoPTv8ParInit                           0x08006edf   Thumb Code    28  anoptv8exapi.o(.text)
    testFun                                  0x08006efb   Thumb Code    62  anoptv8exapi.o(.text)
    testFun2                                 0x08006f39   Thumb Code   152  anoptv8exapi.o(.text)
    setXPid                                  0x08006fd1   Thumb Code   246  anoptv8exapi.o(.text)
    setYPid                                  0x080070c7   Thumb Code   246  anoptv8exapi.o(.text)
    setZPid                                  0x080071bd   Thumb Code   246  anoptv8exapi.o(.text)
    setYawPid                                0x080072b3   Thumb Code   636  anoptv8exapi.o(.text)
    setCamPid                                0x0800752f   Thumb Code   246  anoptv8exapi.o(.text)
    setGPortPid                              0x08007625   Thumb Code   246  anoptv8exapi.o(.text)
    setLaserXPid                             0x0800771b   Thumb Code   246  anoptv8exapi.o(.text)
    setLaserYPid                             0x08007811   Thumb Code   718  anoptv8exapi.o(.text)
    AnoPTv8CmdInit                           0x08007adf   Thumb Code    32  anoptv8exapi.o(.text)
    AnoPTv8FrameAnl                          0x08007aff   Thumb Code    60  anoptv8exapi.o(.text)
    AnoPTv8FrameExchange                     0x08007b3b   Thumb Code   416  anoptv8exapi.o(.text)
    AnoPTv8RunThread1ms                      0x08007d39   Thumb Code   268  anoptv8run.o(.text)
    AnoPTv8GetFreeTxBufIndex                 0x08007e45   Thumb Code   234  anoptv8run.o(.text)
    AnoPTv8RecvOneByte                       0x08007fe1   Thumb Code   250  anoptv8run.o(.text)
    AnoPTv8CmdGetCount                       0x080080e9   Thumb Code     6  anoptv8cmd.o(.text)
    AnoPTv8CmdGetInfo                        0x080080ef   Thumb Code    24  anoptv8cmd.o(.text)
    AnoPTv8CmdFrameAnl                       0x08008107   Thumb Code   160  anoptv8cmd.o(.text)
    AnoPTv8CmdRegister                       0x080081a7   Thumb Code    28  anoptv8cmd.o(.text)
    AnoPTv8CmdGetValsLength                  0x080081c3   Thumb Code   112  anoptv8cmd.o(.text)
    AnoPTv8CmdValCpy                         0x08008233   Thumb Code   206  anoptv8cmd.o(.text)
    AnoPTv8CmdSendIsInIdle                   0x08008301   Thumb Code    14  anoptv8cmd.o(.text)
    AnoPTv8CmdSend                           0x0800830f   Thumb Code   284  anoptv8cmd.o(.text)
    AnoPTv8CmdRecvCheck                      0x0800842b   Thumb Code    50  anoptv8cmd.o(.text)
    AnoPTv8CmdRunThread1ms                   0x0800845d   Thumb Code    80  anoptv8cmd.o(.text)
    AnoPTv8ParGetCount                       0x080084c5   Thumb Code     6  anoptv8par.o(.text)
    anoPTv8ParSetVal                         0x080084cb   Thumb Code  1044  anoptv8par.o(.text)
    AnoPTv8ParFrameAnl                       0x080088df   Thumb Code   158  anoptv8par.o(.text)
    AnoPTv8ParCheckExist                     0x0800897d   Thumb Code    38  anoptv8par.o(.text)
    AnoPTv8ParRegister                       0x080089a3   Thumb Code    42  anoptv8par.o(.text)
    AnoPTv8ParGetInfo                        0x080089cd   Thumb Code    24  anoptv8par.o(.text)
    AnoPTv8ParCpyVal                         0x080089e5   Thumb Code   270  anoptv8par.o(.text)
    AnoPTv8CalFrameCheck                     0x08008afd   Thumb Code    54  anoptv8framefactory.o(.text)
    AnoPTv8SendBuf                           0x08008b33   Thumb Code   254  anoptv8framefactory.o(.text)
    AnoPTv8SendCheck                         0x08008c31   Thumb Code   264  anoptv8framefactory.o(.text)
    AnoPTv8SendDevInfo                       0x08008d39   Thumb Code   524  anoptv8framefactory.o(.text)
    AnoPTv8SendStr                           0x08008f45   Thumb Code   264  anoptv8framefactory.o(.text)
    AnoPTv8SendValStr                        0x0800904d   Thumb Code   356  anoptv8framefactory.o(.text)
    AnoPTv8SendParNum                        0x080091b1   Thumb Code   266  anoptv8framefactory.o(.text)
    AnoPTv8SendParVal                        0x080092bb   Thumb Code   282  anoptv8framefactory.o(.text)
    AnoPTv8SendParInfo                       0x080093d5   Thumb Code   410  anoptv8framefactory.o(.text)
    AnoPTv8SendCmdNum                        0x0800956f   Thumb Code   266  anoptv8framefactory.o(.text)
    AnoPTv8SendCmdInfo                       0x08009679   Thumb Code   486  anoptv8framefactory.o(.text)
    AnoPTv8SendFrame0x0D                     0x0800985f   Thumb Code   260  anoptv8framefactory.o(.text)
    DrvRcInputInit                           0x0800996d   Thumb Code    20  drv_bsp.o(.text)
    All_Init                                 0x08009981   Thumb Code   144  drv_bsp.o(.text)
    DrvPpmGetOneCh                           0x08009a11   Thumb Code    92  drv_bsp.o(.text)
    DrvSbusGetOneByte                        0x08009a6d   Thumb Code   444  drv_bsp.o(.text)
    DrvRcInputTask                           0x08009ce5   Thumb Code   404  drv_bsp.o(.text)
    my_abs                                   0x08009e85   Thumb Code    26  ano_math.o(.text)
    fast_atan2                               0x08009e9f   Thumb Code   350  ano_math.o(.text)
    my_atan                                  0x08009ffd   Thumb Code    24  ano_math.o(.text)
    my_sqrt_reciprocal                       0x0800a015   Thumb Code    76  ano_math.o(.text)
    my_sqrt                                  0x0800a061   Thumb Code    20  ano_math.o(.text)
    mx_sin                                   0x0800a075   Thumb Code   292  ano_math.o(.text)
    my_sin                                   0x0800a199   Thumb Code   108  ano_math.o(.text)
    my_cos                                   0x0800a205   Thumb Code   136  ano_math.o(.text)
    my_sin_deg                               0x0800a28d   Thumb Code   236  ano_math.o(.text)
    my_cos_deg                               0x0800a379   Thumb Code   156  ano_math.o(.text)
    my_deadzone                              0x0800a415   Thumb Code    54  ano_math.o(.text)
    my_deadzone_2                            0x0800a44b   Thumb Code    48  ano_math.o(.text)
    my_HPF                                   0x0800a47b   Thumb Code   346  ano_math.o(.text)
    To_180_degrees_db                        0x0800a5d5   Thumb Code   108  ano_math.o(.text)
    length_limit                             0x0800a641   Thumb Code   140  ano_math.o(.text)
    fifo                                     0x0800a6cd   Thumb Code    42  ano_math.o(.text)
    rot_vec_2                                0x0800a6f7   Thumb Code    84  ano_math.o(.text)
    vec_2_cross_product                      0x0800a74b   Thumb Code    26  ano_math.o(.text)
    vec_2_dot_product                        0x0800a765   Thumb Code    26  ano_math.o(.text)
    vec_3_cross_product_err_sinx             0x0800a77f   Thumb Code    86  ano_math.o(.text)
    vec_3_dot_product                        0x0800a7d5   Thumb Code    38  ano_math.o(.text)
    AnoOF_Check_State                        0x0800a829   Thumb Code   180  drv_anoof.o(.text)
    AnoOFFrameAnl                            0x0800a8dd   Thumb Code   302  drv_anoof.o(.text)
    NMI_Handler                              0x0800aa21   Thumb Code     2  stm32f4xx_it.o(.text)
    MemManage_Handler                        0x0800aa23   Thumb Code     4  stm32f4xx_it.o(.text)
    BusFault_Handler                         0x0800aa27   Thumb Code     4  stm32f4xx_it.o(.text)
    UsageFault_Handler                       0x0800aa2b   Thumb Code     4  stm32f4xx_it.o(.text)
    SVC_Handler                              0x0800aa2f   Thumb Code     2  stm32f4xx_it.o(.text)
    DebugMon_Handler                         0x0800aa31   Thumb Code     2  stm32f4xx_it.o(.text)
    EXTI9_5_IRQHandler                       0x0800aa33   Thumb Code    18  stm32f4xx_it.o(.text)
    TIM3_IRQHandler                          0x0800aa45   Thumb Code     8  stm32f4xx_it.o(.text)
    TIM4_IRQHandler                          0x0800aa4d   Thumb Code     2  stm32f4xx_it.o(.text)
    TIM7_IRQHandler                          0x0800aa4f   Thumb Code    26  stm32f4xx_it.o(.text)
    USART1_IRQHandler                        0x0800aa69   Thumb Code     8  stm32f4xx_it.o(.text)
    USART2_IRQHandler                        0x0800aa71   Thumb Code     8  stm32f4xx_it.o(.text)
    USART3_IRQHandler                        0x0800aa79   Thumb Code     8  stm32f4xx_it.o(.text)
    UART4_IRQHandler                         0x0800aa81   Thumb Code     8  stm32f4xx_it.o(.text)
    UART5_IRQHandler                         0x0800aa89   Thumb Code     8  stm32f4xx_it.o(.text)
    UART7_IRQHandler                         0x0800aa91   Thumb Code     8  stm32f4xx_it.o(.text)
    UART8_IRQHandler                         0x0800aa99   Thumb Code     8  stm32f4xx_it.o(.text)
    USART6_IRQHandler                        0x0800aaa1   Thumb Code     8  stm32f4xx_it.o(.text)
    SysTick_Init                             0x0800aaad   Thumb Code    98  drv_sys.o(.text)
    SysTick_Handler                          0x0800ab0f   Thumb Code    20  drv_sys.o(.text)
    GetSysRunTimeMs                          0x0800ab23   Thumb Code     6  drv_sys.o(.text)
    GetSysRunTimeUs                          0x0800ab29   Thumb Code    62  drv_sys.o(.text)
    MyDelayUs                                0x0800ab67   Thumb Code    24  drv_sys.o(.text)
    MyDelayMs                                0x0800ab7f   Thumb Code    24  drv_sys.o(.text)
    DrvSysInit                               0x0800ab97   Thumb Code    16  drv_sys.o(.text)
    UsbPortCtlInit                           0x0800aba7   Thumb Code    62  drv_sys.o(.text)
    UsbPortCtl                               0x0800abe5   Thumb Code    30  drv_sys.o(.text)
    DvrLedInit                               0x0800ac15   Thumb Code    62  drv_led.o(.text)
    TIM_CONF                                 0x0800ac59   Thumb Code    80  drv_timer.o(.text)
    TIM_NVIC                                 0x0800aca9   Thumb Code    32  drv_timer.o(.text)
    DrvTimerFcInit                           0x0800acc9   Thumb Code    24  drv_timer.o(.text)
    NoUse                                    0x0800ace5   Thumb Code     2  drv_uart.o(.text)
    DrvUart1Init                             0x0800ace7   Thumb Code   246  drv_uart.o(.text)
    DrvUart1SendBuf                          0x0800addd   Thumb Code    74  drv_uart.o(.text)
    drvU1GetByte                             0x0800ae27   Thumb Code    30  drv_uart.o(.text)
    drvU1DataCheck                           0x0800ae45   Thumb Code    52  drv_uart.o(.text)
    Usart1_IRQ                               0x0800ae79   Thumb Code   124  drv_uart.o(.text)
    DrvUart2Init                             0x0800aef5   Thumb Code   240  drv_uart.o(.text)
    DrvUart2SendBuf                          0x0800afe5   Thumb Code    76  drv_uart.o(.text)
    drvU2GetByte                             0x0800b031   Thumb Code    30  drv_uart.o(.text)
    drvU2DataCheck                           0x0800b04f   Thumb Code    52  drv_uart.o(.text)
    Usart2_IRQ                               0x0800b083   Thumb Code   198  drv_uart.o(.text)
    DrvUart3Init                             0x0800b149   Thumb Code   244  drv_uart.o(.text)
    DrvUart3SendBuf                          0x0800b23d   Thumb Code    76  drv_uart.o(.text)
    drvU3GetByte                             0x0800b289   Thumb Code    32  drv_uart.o(.text)
    drvU3DataCheck                           0x0800b2a9   Thumb Code    54  drv_uart.o(.text)
    Usart3_IRQ                               0x0800b2df   Thumb Code   134  drv_uart.o(.text)
    DrvUart4Init                             0x0800b365   Thumb Code   206  drv_uart.o(.text)
    DrvUart4SendBuf                          0x0800b433   Thumb Code    76  drv_uart.o(.text)
    drvU4GetByte                             0x0800b47f   Thumb Code    32  drv_uart.o(.text)
    drvU4DataCheck                           0x0800b49f   Thumb Code    54  drv_uart.o(.text)
    Uart4_IRQ                                0x0800b4d5   Thumb Code   200  drv_uart.o(.text)
    DrvUart5Init                             0x0800b59d   Thumb Code   220  drv_uart.o(.text)
    DrvUart5SendBuf                          0x0800b679   Thumb Code    74  drv_uart.o(.text)
    drvU5GetByte                             0x0800b6c3   Thumb Code    32  drv_uart.o(.text)
    drvU5DataCheck                           0x0800b6e3   Thumb Code    54  drv_uart.o(.text)
    Uart5_IRQ                                0x0800b719   Thumb Code   124  drv_uart.o(.text)
    DrvUart7Init                             0x0800b795   Thumb Code   210  drv_uart.o(.text)
    DrvUart7SendBuf                          0x0800b867   Thumb Code    76  drv_uart.o(.text)
    drvU7GetByte                             0x0800b8b3   Thumb Code    32  drv_uart.o(.text)
    drvU7DataCheck                           0x0800b8d3   Thumb Code    54  drv_uart.o(.text)
    Uart7_IRQ                                0x0800b909   Thumb Code   134  drv_uart.o(.text)
    DrvUart8Init                             0x0800b98f   Thumb Code   276  drv_uart.o(.text)
    DrvUart8SendBuf                          0x0800baa3   Thumb Code    76  drv_uart.o(.text)
    drvU8GetByte                             0x0800baef   Thumb Code    32  drv_uart.o(.text)
    drvU8DataCheck                           0x0800bb0f   Thumb Code    54  drv_uart.o(.text)
    Uart8_IRQ                                0x0800bb45   Thumb Code   134  drv_uart.o(.text)
    DrvUartDataCheck                         0x0800bbcb   Thumb Code    32  drv_uart.o(.text)
    DrvPwmOutInit                            0x0800bc0d   Thumb Code   742  drv_pwmout.o(.text)
    DrvMotorPWMSet                           0x0800bef3   Thumb Code    96  drv_pwmout.o(.text)
    DrvRcPpmInit                             0x0800bf7d   Thumb Code   176  drv_rcin.o(.text)
    PPM_IRQH                                 0x0800c02d   Thumb Code    82  drv_rcin.o(.text)
    DrvRcSbusInit                            0x0800c07f   Thumb Code   166  drv_rcin.o(.text)
    Sbus_IRQH                                0x0800c125   Thumb Code    40  drv_rcin.o(.text)
    DrvRcCrsfInit                            0x0800c14d   Thumb Code     2  drv_rcin.o(.text)
    DrvRcCrsfRxOneByte                       0x0800c189   Thumb Code   398  drv_rcin.o(.text)
    usbd_configure_done_callback             0x0800c33d   Thumb Code    20  drv_usb.o(.text)
    usbd_cdc_acm_bulk_out                    0x0800c351   Thumb Code    38  drv_usb.o(.text)
    usbd_cdc_acm_bulk_in                     0x0800c377   Thumb Code    34  drv_usb.o(.text)
    usb_bsp_inti                             0x0800c399   Thumb Code   244  drv_usb.o(.text)
    DrvUsbInit                               0x0800c48d   Thumb Code    54  drv_usb.o(.text)
    DrvUsbCdcAddTxData                       0x0800c4c3   Thumb Code    50  drv_usb.o(.text)
    DrvUsbRunTask1MS                         0x0800c4f5   Thumb Code   170  drv_usb.o(.text)
    DrvAdcInit                               0x0800c5d9   Thumb Code   354  drv_adc.o(.text)
    DrvAdcCal                                0x0800c73b   Thumb Code    60  drv_adc.o(.text)
    SystemInit                               0x0800c8ad   Thumb Code    88  system_stm32f4xx.o(.text)
    SystemCoreClockUpdate                    0x0800c905   Thumb Code   174  system_stm32f4xx.o(.text)
    NVIC_PriorityGroupConfig                 0x0800c9e1   Thumb Code    54  misc.o(.text)
    NVIC_Init                                0x0800ca17   Thumb Code   164  misc.o(.text)
    NVIC_SetVectorTable                      0x0800cabb   Thumb Code    60  misc.o(.text)
    NVIC_SystemLPConfig                      0x0800caf7   Thumb Code    78  misc.o(.text)
    SysTick_CLKSourceConfig                  0x0800cb45   Thumb Code    62  misc.o(.text)
    ADC_DeInit                               0x0800cba1   Thumb Code    22  stm32f4xx_adc.o(.text)
    ADC_Init                                 0x0800cbb7   Thumb Code   402  stm32f4xx_adc.o(.text)
    ADC_StructInit                           0x0800cd49   Thumb Code    20  stm32f4xx_adc.o(.text)
    ADC_CommonInit                           0x0800cd5d   Thumb Code   342  stm32f4xx_adc.o(.text)
    ADC_CommonStructInit                     0x0800ceb3   Thumb Code    12  stm32f4xx_adc.o(.text)
    ADC_Cmd                                  0x0800cebf   Thumb Code    76  stm32f4xx_adc.o(.text)
    ADC_AnalogWatchdogCmd                    0x0800cf0b   Thumb Code   102  stm32f4xx_adc.o(.text)
    ADC_AnalogWatchdogThresholdsConfig       0x0800cf71   Thumb Code   126  stm32f4xx_adc.o(.text)
    ADC_AnalogWatchdogSingleChannelConfig    0x0800cfef   Thumb Code   136  stm32f4xx_adc.o(.text)
    ADC_TempSensorVrefintCmd                 0x0800d077   Thumb Code    56  stm32f4xx_adc.o(.text)
    ADC_VBATCmd                              0x0800d0af   Thumb Code    56  stm32f4xx_adc.o(.text)
    ADC_RegularChannelConfig                 0x0800d0e7   Thumb Code   408  stm32f4xx_adc.o(.text)
    ADC_SoftwareStartConv                    0x0800d27f   Thumb Code    44  stm32f4xx_adc.o(.text)
    ADC_GetSoftwareStartConvStatus           0x0800d2ab   Thumb Code    54  stm32f4xx_adc.o(.text)
    ADC_EOCOnEachRegularChannelCmd           0x0800d2e1   Thumb Code    76  stm32f4xx_adc.o(.text)
    ADC_ContinuousModeCmd                    0x0800d32d   Thumb Code    76  stm32f4xx_adc.o(.text)
    ADC_DiscModeChannelCountConfig           0x0800d379   Thumb Code   100  stm32f4xx_adc.o(.text)
    ADC_DiscModeCmd                          0x0800d3dd   Thumb Code    76  stm32f4xx_adc.o(.text)
    ADC_GetConversionValue                   0x0800d429   Thumb Code    40  stm32f4xx_adc.o(.text)
    ADC_GetMultiModeConversionValue          0x0800d451   Thumb Code     6  stm32f4xx_adc.o(.text)
    ADC_DMACmd                               0x0800d457   Thumb Code    76  stm32f4xx_adc.o(.text)
    ADC_DMARequestAfterLastTransferCmd       0x0800d4a3   Thumb Code    76  stm32f4xx_adc.o(.text)
    ADC_MultiModeDMARequestAfterLastTransferCmd 0x0800d4ef   Thumb Code    60  stm32f4xx_adc.o(.text)
    ADC_InjectedChannelConfig                0x0800d52b   Thumb Code   332  stm32f4xx_adc.o(.text)
    ADC_InjectedSequencerLengthConfig        0x0800d677   Thumb Code    80  stm32f4xx_adc.o(.text)
    ADC_SetInjectedOffset                    0x0800d6c7   Thumb Code   102  stm32f4xx_adc.o(.text)
    ADC_ExternalTrigInjectedConvConfig       0x0800d72d   Thumb Code   176  stm32f4xx_adc.o(.text)
    ADC_ExternalTrigInjectedConvEdgeConfig   0x0800d7dd   Thumb Code    82  stm32f4xx_adc.o(.text)
    ADC_SoftwareStartInjectedConv            0x0800d82f   Thumb Code    44  stm32f4xx_adc.o(.text)
    ADC_GetSoftwareStartInjectedConvCmdStatus 0x0800d85b   Thumb Code    54  stm32f4xx_adc.o(.text)
    ADC_AutoInjectedConvCmd                  0x0800d891   Thumb Code    76  stm32f4xx_adc.o(.text)
    ADC_InjectedDiscModeCmd                  0x0800d8dd   Thumb Code    76  stm32f4xx_adc.o(.text)
    ADC_GetInjectedConversionValue           0x0800d929   Thumb Code    88  stm32f4xx_adc.o(.text)
    ADC_ITConfig                             0x0800d981   Thumb Code   130  stm32f4xx_adc.o(.text)
    ADC_GetFlagStatus                        0x0800da03   Thumb Code    90  stm32f4xx_adc.o(.text)
    ADC_ClearFlag                            0x0800da5d   Thumb Code    62  stm32f4xx_adc.o(.text)
    ADC_GetITStatus                          0x0800da9b   Thumb Code   122  stm32f4xx_adc.o(.text)
    ADC_ClearITPendingBit                    0x0800db15   Thumb Code    88  stm32f4xx_adc.o(.text)
    DMA_DeInit                               0x0800db7d   Thumb Code   462  stm32f4xx_dma.o(.text)
    DMA_Init                                 0x0800dd4b   Thumb Code   684  stm32f4xx_dma.o(.text)
    DMA_StructInit                           0x0800dff7   Thumb Code    34  stm32f4xx_dma.o(.text)
    DMA_Cmd                                  0x0800e019   Thumb Code   182  stm32f4xx_dma.o(.text)
    DMA_PeriphIncOffsetSizeConfig            0x0800e0cf   Thumb Code   184  stm32f4xx_dma.o(.text)
    DMA_FlowControllerConfig                 0x0800e187   Thumb Code   182  stm32f4xx_dma.o(.text)
    DMA_SetCurrDataCounter                   0x0800e23d   Thumb Code   146  stm32f4xx_dma.o(.text)
    DMA_GetCurrDataCounter                   0x0800e2cf   Thumb Code   146  stm32f4xx_dma.o(.text)
    DMA_DoubleBufferModeConfig               0x0800e361   Thumb Code   204  stm32f4xx_dma.o(.text)
    DMA_DoubleBufferModeCmd                  0x0800e42d   Thumb Code   182  stm32f4xx_dma.o(.text)
    DMA_MemoryTargetConfig                   0x0800e4e3   Thumb Code   174  stm32f4xx_dma.o(.text)
    DMA_GetCurrentMemoryTarget               0x0800e591   Thumb Code   160  stm32f4xx_dma.o(.text)
    DMA_GetCmdStatus                         0x0800e631   Thumb Code   160  stm32f4xx_dma.o(.text)
    DMA_GetFIFOStatus                        0x0800e6d1   Thumb Code   152  stm32f4xx_dma.o(.text)
    DMA_GetFlagStatus                        0x0800e769   Thumb Code   486  stm32f4xx_dma.o(.text)
    DMA_ClearFlag                            0x0800e94f   Thumb Code   216  stm32f4xx_dma.o(.text)
    DMA_ITConfig                             0x0800ea27   Thumb Code   238  stm32f4xx_dma.o(.text)
    DMA_GetITStatus                          0x0800eb15   Thumb Code   590  stm32f4xx_dma.o(.text)
    DMA_ClearITPendingBit                    0x0800ed63   Thumb Code   216  stm32f4xx_dma.o(.text)
    EXTI_DeInit                              0x0800eee5   Thumb Code    28  stm32f4xx_exti.o(.text)
    EXTI_Init                                0x0800ef01   Thumb Code   236  stm32f4xx_exti.o(.text)
    EXTI_StructInit                          0x0800efed   Thumb Code    16  stm32f4xx_exti.o(.text)
    EXTI_GenerateSWInterrupt                 0x0800effd   Thumb Code    38  stm32f4xx_exti.o(.text)
    EXTI_GetFlagStatus                       0x0800f023   Thumb Code   164  stm32f4xx_exti.o(.text)
    EXTI_ClearFlag                           0x0800f0c7   Thumb Code    30  stm32f4xx_exti.o(.text)
    EXTI_GetITStatus                         0x0800f0e5   Thumb Code   166  stm32f4xx_exti.o(.text)
    EXTI_ClearITPendingBit                   0x0800f18b   Thumb Code    32  stm32f4xx_exti.o(.text)
    GPIO_DeInit                              0x0800f1b9   Thumb Code   344  stm32f4xx_gpio.o(.text)
    GPIO_Init                                0x0800f311   Thumb Code   352  stm32f4xx_gpio.o(.text)
    GPIO_StructInit                          0x0800f471   Thumb Code    18  stm32f4xx_gpio.o(.text)
    GPIO_PinLockConfig                       0x0800f483   Thumb Code   132  stm32f4xx_gpio.o(.text)
    GPIO_ReadInputDataBit                    0x0800f507   Thumb Code   244  stm32f4xx_gpio.o(.text)
    GPIO_ReadInputData                       0x0800f5fb   Thumb Code    88  stm32f4xx_gpio.o(.text)
    GPIO_ReadOutputDataBit                   0x0800f653   Thumb Code   194  stm32f4xx_gpio.o(.text)
    GPIO_ReadOutputData                      0x0800f715   Thumb Code    88  stm32f4xx_gpio.o(.text)
    GPIO_SetBits                             0x0800f76d   Thumb Code   104  stm32f4xx_gpio.o(.text)
    GPIO_ResetBits                           0x0800f7d5   Thumb Code   104  stm32f4xx_gpio.o(.text)
    GPIO_WriteBit                            0x0800f83d   Thumb Code   206  stm32f4xx_gpio.o(.text)
    GPIO_Write                               0x0800f90b   Thumb Code    88  stm32f4xx_gpio.o(.text)
    GPIO_ToggleBits                          0x0800f963   Thumb Code    92  stm32f4xx_gpio.o(.text)
    GPIO_PinAFConfig                         0x0800f9bf   Thumb Code   428  stm32f4xx_gpio.o(.text)
    RCC_DeInit                               0x0800fb91   Thumb Code    82  stm32f4xx_rcc.o(.text)
    RCC_HSEConfig                            0x0800fbe3   Thumb Code    40  stm32f4xx_rcc.o(.text)
    RCC_GetFlagStatus                        0x0800fc0b   Thumb Code   134  stm32f4xx_rcc.o(.text)
    RCC_WaitForHSEStartUp                    0x0800fc91   Thumb Code    56  stm32f4xx_rcc.o(.text)
    RCC_AdjustHSICalibrationValue            0x0800fcc9   Thumb Code    38  stm32f4xx_rcc.o(.text)
    RCC_HSICmd                               0x0800fcef   Thumb Code    28  stm32f4xx_rcc.o(.text)
    RCC_LSEConfig                            0x0800fd0b   Thumb Code    72  stm32f4xx_rcc.o(.text)
    RCC_LSICmd                               0x0800fd53   Thumb Code    28  stm32f4xx_rcc.o(.text)
    RCC_PLLConfig                            0x0800fd6f   Thumb Code   156  stm32f4xx_rcc.o(.text)
    RCC_PLLCmd                               0x0800fe0b   Thumb Code    28  stm32f4xx_rcc.o(.text)
    RCC_PLLI2SConfig                         0x0800fe27   Thumb Code    88  stm32f4xx_rcc.o(.text)
    RCC_PLLI2SCmd                            0x0800fe7f   Thumb Code    28  stm32f4xx_rcc.o(.text)
    RCC_PLLSAIConfig                         0x0800fe9b   Thumb Code    88  stm32f4xx_rcc.o(.text)
    RCC_PLLSAICmd                            0x0800fef3   Thumb Code    28  stm32f4xx_rcc.o(.text)
    RCC_ClockSecuritySystemCmd               0x0800ff0f   Thumb Code    28  stm32f4xx_rcc.o(.text)
    RCC_MCO1Config                           0x0800ff2b   Thumb Code   128  stm32f4xx_rcc.o(.text)
    RCC_MCO2Config                           0x0800ffab   Thumb Code    98  stm32f4xx_rcc.o(.text)
    RCC_SYSCLKConfig                         0x0801000d   Thumb Code    42  stm32f4xx_rcc.o(.text)
    RCC_GetSYSCLKSource                      0x08010037   Thumb Code    10  stm32f4xx_rcc.o(.text)
    RCC_HCLKConfig                           0x08010041   Thumb Code    66  stm32f4xx_rcc.o(.text)
    RCC_PCLK1Config                          0x08010083   Thumb Code    58  stm32f4xx_rcc.o(.text)
    RCC_PCLK2Config                          0x080100bd   Thumb Code    60  stm32f4xx_rcc.o(.text)
    RCC_GetClocksFreq                        0x080100f9   Thumb Code   214  stm32f4xx_rcc.o(.text)
    RCC_RTCCLKConfig                         0x080101cf   Thumb Code   260  stm32f4xx_rcc.o(.text)
    RCC_RTCCLKCmd                            0x080102d3   Thumb Code    28  stm32f4xx_rcc.o(.text)
    RCC_BackupResetCmd                       0x080102ef   Thumb Code    30  stm32f4xx_rcc.o(.text)
    RCC_I2SCLKConfig                         0x0801030d   Thumb Code    28  stm32f4xx_rcc.o(.text)
    RCC_SAIBlockACLKConfig                   0x08010329   Thumb Code    48  stm32f4xx_rcc.o(.text)
    RCC_SAIBlockBCLKConfig                   0x08010359   Thumb Code   200  stm32f4xx_rcc.o(.text)
    RCC_SAIPLLI2SClkDivConfig                0x08010421   Thumb Code    44  stm32f4xx_rcc.o(.text)
    RCC_SAIPLLSAIClkDivConfig                0x0801044d   Thumb Code    46  stm32f4xx_rcc.o(.text)
    RCC_LTDCCLKDivConfig                     0x0801047b   Thumb Code    52  stm32f4xx_rcc.o(.text)
    RCC_TIMCLKPresConfig                     0x080104af   Thumb Code    28  stm32f4xx_rcc.o(.text)
    RCC_AHB1PeriphClockCmd                   0x080104cb   Thumb Code    78  stm32f4xx_rcc.o(.text)
    RCC_AHB2PeriphClockCmd                   0x08010519   Thumb Code    78  stm32f4xx_rcc.o(.text)
    RCC_AHB3PeriphClockCmd                   0x08010567   Thumb Code    78  stm32f4xx_rcc.o(.text)
    RCC_APB1PeriphClockCmd                   0x080105b5   Thumb Code    78  stm32f4xx_rcc.o(.text)
    RCC_APB2PeriphClockCmd                   0x08010603   Thumb Code    78  stm32f4xx_rcc.o(.text)
    RCC_AHB1PeriphResetCmd                   0x08010651   Thumb Code    78  stm32f4xx_rcc.o(.text)
    RCC_AHB2PeriphResetCmd                   0x0801069f   Thumb Code    78  stm32f4xx_rcc.o(.text)
    RCC_AHB3PeriphResetCmd                   0x080106ed   Thumb Code    78  stm32f4xx_rcc.o(.text)
    RCC_APB1PeriphResetCmd                   0x0801073b   Thumb Code    78  stm32f4xx_rcc.o(.text)
    RCC_APB2PeriphResetCmd                   0x08010789   Thumb Code    78  stm32f4xx_rcc.o(.text)
    RCC_AHB1PeriphClockLPModeCmd             0x080107d7   Thumb Code   102  stm32f4xx_rcc.o(.text)
    RCC_AHB2PeriphClockLPModeCmd             0x0801083d   Thumb Code    78  stm32f4xx_rcc.o(.text)
    RCC_AHB3PeriphClockLPModeCmd             0x0801088b   Thumb Code    78  stm32f4xx_rcc.o(.text)
    RCC_APB1PeriphClockLPModeCmd             0x080108d9   Thumb Code    78  stm32f4xx_rcc.o(.text)
    RCC_APB2PeriphClockLPModeCmd             0x08010927   Thumb Code    78  stm32f4xx_rcc.o(.text)
    RCC_LSEModeConfig                        0x08010975   Thumb Code    62  stm32f4xx_rcc.o(.text)
    RCC_ITConfig                             0x080109b3   Thumb Code    78  stm32f4xx_rcc.o(.text)
    RCC_ClearFlag                            0x08010a01   Thumb Code    18  stm32f4xx_rcc.o(.text)
    RCC_GetITStatus                          0x08010a13   Thumb Code    70  stm32f4xx_rcc.o(.text)
    RCC_ClearITPendingBit                    0x08010a59   Thumb Code    26  stm32f4xx_rcc.o(.text)
    TIM_DeInit                               0x08010a81   Thumb Code   440  stm32f4xx_tim.o(.text)
    TIM_TimeBaseInit                         0x08010c39   Thumb Code   278  stm32f4xx_tim.o(.text)
    TIM_TimeBaseStructInit                   0x08010d4f   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_PrescalerConfig                      0x08010d61   Thumb Code   128  stm32f4xx_tim.o(.text)
    TIM_CounterModeConfig                    0x08010de1   Thumb Code   100  stm32f4xx_tim.o(.text)
    TIM_SetCounter                           0x08010e45   Thumb Code   164  stm32f4xx_tim.o(.text)
    TIM_SetAutoreload                        0x08010ee9   Thumb Code   106  stm32f4xx_tim.o(.text)
    TIM_GetCounter                           0x08010f53   Thumb Code   104  stm32f4xx_tim.o(.text)
    TIM_GetPrescaler                         0x08010fbb   Thumb Code   104  stm32f4xx_tim.o(.text)
    TIM_UpdateDisableConfig                  0x08011023   Thumb Code   144  stm32f4xx_tim.o(.text)
    TIM_UpdateRequestConfig                  0x080110b3   Thumb Code   144  stm32f4xx_tim.o(.text)
    TIM_ARRPreloadConfig                     0x08011143   Thumb Code   144  stm32f4xx_tim.o(.text)
    TIM_SelectOnePulseMode                   0x080111d3   Thumb Code   138  stm32f4xx_tim.o(.text)
    TIM_SetClockDivision                     0x0801125d   Thumb Code   190  stm32f4xx_tim.o(.text)
    TIM_Cmd                                  0x0801131b   Thumb Code   144  stm32f4xx_tim.o(.text)
    TIM_OC1Init                              0x080113ab   Thumb Code   400  stm32f4xx_tim.o(.text)
    TIM_OC2Init                              0x0801153b   Thumb Code   474  stm32f4xx_tim.o(.text)
    TIM_OC3Init                              0x08011715   Thumb Code   400  stm32f4xx_tim.o(.text)
    TIM_OC4Init                              0x080118a5   Thumb Code   290  stm32f4xx_tim.o(.text)
    TIM_OCStructInit                         0x080119c7   Thumb Code    20  stm32f4xx_tim.o(.text)
    TIM_SelectOCxM                           0x080119db   Thumb Code   250  stm32f4xx_tim.o(.text)
    TIM_SetCompare1                          0x08011ad5   Thumb Code   144  stm32f4xx_tim.o(.text)
    TIM_SetCompare2                          0x08011b65   Thumb Code    70  stm32f4xx_tim.o(.text)
    TIM_SetCompare3                          0x08011bab   Thumb Code    58  stm32f4xx_tim.o(.text)
    TIM_SetCompare4                          0x08011be5   Thumb Code    58  stm32f4xx_tim.o(.text)
    TIM_ForcedOC1Config                      0x08011c1f   Thumb Code   126  stm32f4xx_tim.o(.text)
    TIM_ForcedOC2Config                      0x08011c9d   Thumb Code   110  stm32f4xx_tim.o(.text)
    TIM_ForcedOC3Config                      0x08011d0b   Thumb Code    90  stm32f4xx_tim.o(.text)
    TIM_ForcedOC4Config                      0x08011d65   Thumb Code    98  stm32f4xx_tim.o(.text)
    TIM_OC1PreloadConfig                     0x08011dc7   Thumb Code   124  stm32f4xx_tim.o(.text)
    TIM_OC2PreloadConfig                     0x08011e43   Thumb Code   108  stm32f4xx_tim.o(.text)
    TIM_OC3PreloadConfig                     0x08011eaf   Thumb Code    88  stm32f4xx_tim.o(.text)
    TIM_OC4PreloadConfig                     0x08011f07   Thumb Code   146  stm32f4xx_tim.o(.text)
    TIM_OC1FastConfig                        0x08011f99   Thumb Code   124  stm32f4xx_tim.o(.text)
    TIM_OC2FastConfig                        0x08012015   Thumb Code   108  stm32f4xx_tim.o(.text)
    TIM_OC3FastConfig                        0x08012081   Thumb Code    88  stm32f4xx_tim.o(.text)
    TIM_OC4FastConfig                        0x080120d9   Thumb Code    96  stm32f4xx_tim.o(.text)
    TIM_ClearOC1Ref                          0x08012139   Thumb Code   124  stm32f4xx_tim.o(.text)
    TIM_ClearOC2Ref                          0x080121b5   Thumb Code   106  stm32f4xx_tim.o(.text)
    TIM_ClearOC3Ref                          0x0801221f   Thumb Code    88  stm32f4xx_tim.o(.text)
    TIM_ClearOC4Ref                          0x08012277   Thumb Code    94  stm32f4xx_tim.o(.text)
    TIM_OC1PolarityConfig                    0x080122d5   Thumb Code   124  stm32f4xx_tim.o(.text)
    TIM_OC1NPolarityConfig                   0x08012351   Thumb Code   114  stm32f4xx_tim.o(.text)
    TIM_OC2PolarityConfig                    0x080123c3   Thumb Code   108  stm32f4xx_tim.o(.text)
    TIM_OC2NPolarityConfig                   0x0801242f   Thumb Code    72  stm32f4xx_tim.o(.text)
    TIM_OC3PolarityConfig                    0x08012477   Thumb Code    96  stm32f4xx_tim.o(.text)
    TIM_OC3NPolarityConfig                   0x080124d7   Thumb Code    72  stm32f4xx_tim.o(.text)
    TIM_OC4PolarityConfig                    0x0801251f   Thumb Code    96  stm32f4xx_tim.o(.text)
    TIM_CCxCmd                               0x0801257f   Thumb Code   168  stm32f4xx_tim.o(.text)
    TIM_CCxNCmd                              0x08012627   Thumb Code   104  stm32f4xx_tim.o(.text)
    TIM_SetIC4Prescaler                      0x0801268f   Thumb Code   106  stm32f4xx_tim.o(.text)
    TIM_SetIC3Prescaler                      0x08012749   Thumb Code   148  stm32f4xx_tim.o(.text)
    TIM_SetIC2Prescaler                      0x08012825   Thumb Code   118  stm32f4xx_tim.o(.text)
    TIM_SetIC1Prescaler                      0x080128eb   Thumb Code   134  stm32f4xx_tim.o(.text)
    TIM_ICInit                               0x080129ab   Thumb Code   460  stm32f4xx_tim.o(.text)
    TIM_ICStructInit                         0x08012b77   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_PWMIConfig                           0x08012b89   Thumb Code   234  stm32f4xx_tim.o(.text)
    TIM_GetCapture1                          0x08012c73   Thumb Code    92  stm32f4xx_tim.o(.text)
    TIM_GetCapture2                          0x08012ccf   Thumb Code    68  stm32f4xx_tim.o(.text)
    TIM_GetCapture3                          0x08012d13   Thumb Code    56  stm32f4xx_tim.o(.text)
    TIM_GetCapture4                          0x08012d4b   Thumb Code    56  stm32f4xx_tim.o(.text)
    TIM_BDTRConfig                           0x08012d83   Thumb Code   222  stm32f4xx_tim.o(.text)
    TIM_BDTRStructInit                       0x08012e61   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_CtrlPWMOutputs                       0x08012e73   Thumb Code    78  stm32f4xx_tim.o(.text)
    TIM_SelectCOM                            0x08012ec1   Thumb Code    72  stm32f4xx_tim.o(.text)
    TIM_CCPreloadControl                     0x08012f09   Thumb Code    72  stm32f4xx_tim.o(.text)
    TIM_ITConfig                             0x08012f51   Thumb Code   216  stm32f4xx_tim.o(.text)
    TIM_GenerateEvent                        0x08013029   Thumb Code   126  stm32f4xx_tim.o(.text)
    TIM_GetFlagStatus                        0x080130a7   Thumb Code   188  stm32f4xx_tim.o(.text)
    TIM_ClearFlag                            0x08013163   Thumb Code   108  stm32f4xx_tim.o(.text)
    TIM_GetITStatus                          0x080131cf   Thumb Code   186  stm32f4xx_tim.o(.text)
    TIM_ClearITPendingBit                    0x08013289   Thumb Code   108  stm32f4xx_tim.o(.text)
    TIM_DMAConfig                            0x080132f5   Thumb Code   328  stm32f4xx_tim.o(.text)
    TIM_DMACmd                               0x0801343d   Thumb Code   126  stm32f4xx_tim.o(.text)
    TIM_SelectCCDMA                          0x080134bb   Thumb Code    96  stm32f4xx_tim.o(.text)
    TIM_InternalClockConfig                  0x0801351b   Thumb Code    76  stm32f4xx_tim.o(.text)
    TIM_SelectInputTrigger                   0x08013567   Thumb Code   148  stm32f4xx_tim.o(.text)
    TIM_ITRxExternalClockConfig              0x080135fb   Thumb Code   110  stm32f4xx_tim.o(.text)
    TIM_TIxExternalClockConfig               0x08013669   Thumb Code   184  stm32f4xx_tim.o(.text)
    TIM_ETRConfig                            0x08013721   Thumb Code   164  stm32f4xx_tim.o(.text)
    TIM_ETRClockMode1Config                  0x080137c5   Thumb Code   228  stm32f4xx_tim.o(.text)
    TIM_ETRClockMode2Config                  0x080138a9   Thumb Code   152  stm32f4xx_tim.o(.text)
    TIM_SelectOutputTrigger                  0x08013941   Thumb Code   126  stm32f4xx_tim.o(.text)
    TIM_SelectSlaveMode                      0x080139bf   Thumb Code   112  stm32f4xx_tim.o(.text)
    TIM_SelectMasterSlaveMode                0x08013a2f   Thumb Code   102  stm32f4xx_tim.o(.text)
    TIM_EncoderInterfaceConfig               0x08013a95   Thumb Code   226  stm32f4xx_tim.o(.text)
    TIM_SelectHallSensor                     0x08013b77   Thumb Code   108  stm32f4xx_tim.o(.text)
    TIM_RemapConfig                          0x08013be3   Thumb Code   138  stm32f4xx_tim.o(.text)
    USART_DeInit                             0x08013c6d   Thumb Code   276  stm32f4xx_usart.o(.text)
    USART_Init                               0x08013d81   Thumb Code   492  stm32f4xx_usart.o(.text)
    USART_StructInit                         0x08013f6d   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_ClockInit                          0x08013f85   Thumb Code   166  stm32f4xx_usart.o(.text)
    USART_ClockStructInit                    0x0801402b   Thumb Code    12  stm32f4xx_usart.o(.text)
    USART_Cmd                                0x08014037   Thumb Code   170  stm32f4xx_usart.o(.text)
    USART_SetPrescaler                       0x080140e1   Thumb Code    94  stm32f4xx_usart.o(.text)
    USART_OverSampling8Cmd                   0x0801413f   Thumb Code   118  stm32f4xx_usart.o(.text)
    USART_OneBitMethodCmd                    0x080141b5   Thumb Code   120  stm32f4xx_usart.o(.text)
    USART_SendData                           0x0801422d   Thumb Code   104  stm32f4xx_usart.o(.text)
    USART_ReceiveData                        0x08014295   Thumb Code    84  stm32f4xx_usart.o(.text)
    USART_SetAddress                         0x080142e9   Thumb Code   112  stm32f4xx_usart.o(.text)
    USART_ReceiverWakeUpCmd                  0x08014359   Thumb Code   120  stm32f4xx_usart.o(.text)
    USART_WakeUpConfig                       0x080143d1   Thumb Code   116  stm32f4xx_usart.o(.text)
    USART_LINBreakDetectLengthConfig         0x08014445   Thumb Code   158  stm32f4xx_usart.o(.text)
    USART_LINCmd                             0x080144e3   Thumb Code   120  stm32f4xx_usart.o(.text)
    USART_SendBreak                          0x0801455b   Thumb Code    86  stm32f4xx_usart.o(.text)
    USART_HalfDuplexCmd                      0x080145b1   Thumb Code   120  stm32f4xx_usart.o(.text)
    USART_SetGuardTime                       0x08014629   Thumb Code    58  stm32f4xx_usart.o(.text)
    USART_SmartCardCmd                       0x08014663   Thumb Code    84  stm32f4xx_usart.o(.text)
    USART_SmartCardNACKCmd                   0x080146b7   Thumb Code    84  stm32f4xx_usart.o(.text)
    USART_IrDAConfig                         0x0801470b   Thumb Code   114  stm32f4xx_usart.o(.text)
    USART_IrDACmd                            0x0801477d   Thumb Code   120  stm32f4xx_usart.o(.text)
    USART_DMACmd                             0x080147f5   Thumb Code   138  stm32f4xx_usart.o(.text)
    USART_ITConfig                           0x0801487f   Thumb Code   330  stm32f4xx_usart.o(.text)
    USART_GetFlagStatus                      0x080149c9   Thumb Code   194  stm32f4xx_usart.o(.text)
    USART_ClearFlag                          0x08014a8b   Thumb Code   152  stm32f4xx_usart.o(.text)
    USART_GetITStatus                        0x08014b23   Thumb Code   302  stm32f4xx_usart.o(.text)
    USART_ClearITPendingBit                  0x08014c51   Thumb Code   234  stm32f4xx_usart.o(.text)
    Reset_Handler                            0x08014d51   Thumb Code     8  startup_stm32f429_439xx.o(.text)
    HardFault_Handler                        0x08014d5b   Thumb Code     2  startup_stm32f429_439xx.o(.text)
    PendSV_Handler                           0x08014d67   Thumb Code     2  startup_stm32f429_439xx.o(.text)
    ADC_IRQHandler                           0x08014d6b   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    CAN1_RX0_IRQHandler                      0x08014d6b   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    CAN1_RX1_IRQHandler                      0x08014d6b   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    CAN1_SCE_IRQHandler                      0x08014d6b   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    CAN1_TX_IRQHandler                       0x08014d6b   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    CAN2_RX0_IRQHandler                      0x08014d6b   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    CAN2_RX1_IRQHandler                      0x08014d6b   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    CAN2_SCE_IRQHandler                      0x08014d6b   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    CAN2_TX_IRQHandler                       0x08014d6b   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    CRYP_IRQHandler                          0x08014d6b   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    DCMI_IRQHandler                          0x08014d6b   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    DMA1_Stream0_IRQHandler                  0x08014d6b   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    DMA1_Stream1_IRQHandler                  0x08014d6b   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    DMA1_Stream2_IRQHandler                  0x08014d6b   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    DMA1_Stream3_IRQHandler                  0x08014d6b   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    DMA1_Stream4_IRQHandler                  0x08014d6b   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    DMA1_Stream5_IRQHandler                  0x08014d6b   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    DMA1_Stream6_IRQHandler                  0x08014d6b   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    DMA1_Stream7_IRQHandler                  0x08014d6b   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    DMA2D_IRQHandler                         0x08014d6b   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    DMA2_Stream0_IRQHandler                  0x08014d6b   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    DMA2_Stream1_IRQHandler                  0x08014d6b   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    DMA2_Stream2_IRQHandler                  0x08014d6b   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    DMA2_Stream3_IRQHandler                  0x08014d6b   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    DMA2_Stream4_IRQHandler                  0x08014d6b   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    DMA2_Stream5_IRQHandler                  0x08014d6b   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    DMA2_Stream6_IRQHandler                  0x08014d6b   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    DMA2_Stream7_IRQHandler                  0x08014d6b   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    ETH_IRQHandler                           0x08014d6b   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    ETH_WKUP_IRQHandler                      0x08014d6b   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    EXTI0_IRQHandler                         0x08014d6b   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    EXTI15_10_IRQHandler                     0x08014d6b   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    EXTI1_IRQHandler                         0x08014d6b   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    EXTI2_IRQHandler                         0x08014d6b   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    EXTI3_IRQHandler                         0x08014d6b   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    EXTI4_IRQHandler                         0x08014d6b   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    FLASH_IRQHandler                         0x08014d6b   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    FMC_IRQHandler                           0x08014d6b   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    FPU_IRQHandler                           0x08014d6b   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    HASH_RNG_IRQHandler                      0x08014d6b   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    I2C1_ER_IRQHandler                       0x08014d6b   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    I2C1_EV_IRQHandler                       0x08014d6b   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    I2C2_ER_IRQHandler                       0x08014d6b   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    I2C2_EV_IRQHandler                       0x08014d6b   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    I2C3_ER_IRQHandler                       0x08014d6b   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    I2C3_EV_IRQHandler                       0x08014d6b   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    LTDC_ER_IRQHandler                       0x08014d6b   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    LTDC_IRQHandler                          0x08014d6b   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    OTG_FS_WKUP_IRQHandler                   0x08014d6b   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    OTG_HS_EP1_IN_IRQHandler                 0x08014d6b   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    OTG_HS_EP1_OUT_IRQHandler                0x08014d6b   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    OTG_HS_IRQHandler                        0x08014d6b   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    OTG_HS_WKUP_IRQHandler                   0x08014d6b   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    PVD_IRQHandler                           0x08014d6b   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    RCC_IRQHandler                           0x08014d6b   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    RTC_Alarm_IRQHandler                     0x08014d6b   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    RTC_WKUP_IRQHandler                      0x08014d6b   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    SAI1_IRQHandler                          0x08014d6b   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    SDIO_IRQHandler                          0x08014d6b   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    SPI1_IRQHandler                          0x08014d6b   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    SPI2_IRQHandler                          0x08014d6b   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    SPI3_IRQHandler                          0x08014d6b   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    SPI4_IRQHandler                          0x08014d6b   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    SPI5_IRQHandler                          0x08014d6b   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    SPI6_IRQHandler                          0x08014d6b   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    TAMP_STAMP_IRQHandler                    0x08014d6b   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    TIM1_BRK_TIM9_IRQHandler                 0x08014d6b   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    TIM1_CC_IRQHandler                       0x08014d6b   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    TIM1_TRG_COM_TIM11_IRQHandler            0x08014d6b   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    TIM1_UP_TIM10_IRQHandler                 0x08014d6b   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    TIM2_IRQHandler                          0x08014d6b   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    TIM5_IRQHandler                          0x08014d6b   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    TIM6_DAC_IRQHandler                      0x08014d6b   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    TIM8_BRK_TIM12_IRQHandler                0x08014d6b   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    TIM8_CC_IRQHandler                       0x08014d6b   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    TIM8_TRG_COM_TIM14_IRQHandler            0x08014d6b   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    TIM8_UP_TIM13_IRQHandler                 0x08014d6b   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    WWDG_IRQHandler                          0x08014d6b   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    dwc2_ep_write                            0x08014f39   Thumb Code    34  usb_dc_dwc2.o(.text)
    dwc2_ep_read                             0x08014f5b   Thumb Code    28  usb_dc_dwc2.o(.text)
    usb_dc_low_level_init                    0x0801509f   Thumb Code     2  usb_dc_dwc2.o(.text)
    usb_dc_low_level_deinit                  0x080150a1   Thumb Code     2  usb_dc_dwc2.o(.text)
    usb_dc_init                              0x080150a3   Thumb Code   390  usb_dc_dwc2.o(.text)
    usb_dc_deinit                            0x08015229   Thumb Code    84  usb_dc_dwc2.o(.text)
    usbd_set_address                         0x0801527d   Thumb Code    42  usb_dc_dwc2.o(.text)
    usbd_get_port_speed                      0x080152a7   Thumb Code    34  usb_dc_dwc2.o(.text)
    usbd_ep_open                             0x080152c9   Thumb Code   292  usb_dc_dwc2.o(.text)
    usbd_ep_close                            0x080153ed   Thumb Code   222  usb_dc_dwc2.o(.text)
    usbd_ep_set_stall                        0x080154cb   Thumb Code   150  usb_dc_dwc2.o(.text)
    usbd_ep_clear_stall                      0x08015561   Thumb Code   208  usb_dc_dwc2.o(.text)
    usbd_ep_is_stalled                       0x08015631   Thumb Code     6  usb_dc_dwc2.o(.text)
    usbd_ep_start_write                      0x08015637   Thumb Code   498  usb_dc_dwc2.o(.text)
    usbd_ep_start_read                       0x08015829   Thumb Code   484  usb_dc_dwc2.o(.text)
    OTG_FS_IRQHandler                        0x08015a0d   Thumb Code  1248  usb_dc_dwc2.o(.text)
    usbd_cdc_acm_get_line_coding             0x08015f7d   Thumb Code    16  usbd_cdc.o(.text)
    usbd_cdc_acm_set_rts                     0x08015f8d   Thumb Code     2  usbd_cdc.o(.text)
    usbd_cdc_acm_set_dtr                     0x08015f8f   Thumb Code     2  usbd_cdc.o(.text)
    usbd_cdc_acm_set_line_coding             0x08015f91   Thumb Code     2  usbd_cdc.o(.text)
    usbd_cdc_acm_init_intf                   0x0801602b   Thumb Code    14  usbd_cdc.o(.text)
    usbd_event_handler                       0x080162a9   Thumb Code    50  usbd_core.o(.text)
    usbd_event_connect_handler               0x0801673d   Thumb Code    10  usbd_core.o(.text)
    usbd_event_disconnect_handler            0x08016747   Thumb Code    10  usbd_core.o(.text)
    usbd_event_resume_handler                0x08016751   Thumb Code    10  usbd_core.o(.text)
    usbd_event_suspend_handler               0x0801675b   Thumb Code    10  usbd_core.o(.text)
    usbd_event_reset_handler                 0x08016765   Thumb Code    72  usbd_core.o(.text)
    usbd_event_ep0_setup_complete_handler    0x080167ad   Thumb Code   192  usbd_core.o(.text)
    usbd_event_ep0_in_complete_handler       0x0801686d   Thumb Code    98  usbd_core.o(.text)
    usbd_event_ep0_out_complete_handler      0x080168cf   Thumb Code    90  usbd_core.o(.text)
    usbd_event_ep_in_complete_handler        0x08016929   Thumb Code    68  usbd_core.o(.text)
    usbd_event_ep_out_complete_handler       0x0801696d   Thumb Code    46  usbd_core.o(.text)
    usbd_desc_register                       0x0801699b   Thumb Code    52  usbd_core.o(.text)
    usbd_msosv1_desc_register                0x080169cf   Thumb Code     6  usbd_core.o(.text)
    usbd_msosv2_desc_register                0x080169d5   Thumb Code     6  usbd_core.o(.text)
    usbd_bos_desc_register                   0x080169db   Thumb Code     6  usbd_core.o(.text)
    usbd_add_interface                       0x080169e1   Thumb Code    48  usbd_core.o(.text)
    usbd_add_endpoint                        0x08016a11   Thumb Code    80  usbd_core.o(.text)
    usb_device_is_configured                 0x08016a61   Thumb Code     8  usbd_core.o(.text)
    usbd_initialize                          0x08016a69   Thumb Code     8  usbd_core.o(.text)
    usbd_deinitialize                        0x08016a71   Thumb Code    26  usbd_core.o(.text)
    __aeabi_memcpy                           0x08016aa9   Thumb Code    36  memcpya.o(.text)
    __aeabi_memcpy4                          0x08016aa9   Thumb Code     0  memcpya.o(.text)
    __aeabi_memcpy8                          0x08016aa9   Thumb Code     0  memcpya.o(.text)
    __aeabi_memset                           0x08016acd   Thumb Code    14  memseta.o(.text)
    __aeabi_memset4                          0x08016acd   Thumb Code     0  memseta.o(.text)
    __aeabi_memset8                          0x08016acd   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr                           0x08016adb   Thumb Code     4  memseta.o(.text)
    __aeabi_memclr4                          0x08016adb   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr8                          0x08016adb   Thumb Code     0  memseta.o(.text)
    _memset$wrapper                          0x08016adf   Thumb Code    18  memseta.o(.text)
    strcat                                   0x08016af1   Thumb Code    24  strcat.o(.text)
    memcmp                                   0x08016b09   Thumb Code    26  memcmp.o(.text)
    __aeabi_dadd                             0x08016b23   Thumb Code   322  dadd.o(.text)
    __aeabi_dsub                             0x08016c65   Thumb Code     6  dadd.o(.text)
    __aeabi_drsub                            0x08016c6b   Thumb Code     6  dadd.o(.text)
    __aeabi_dmul                             0x08016c71   Thumb Code   228  dmul.o(.text)
    __aeabi_l2f                              0x08016d55   Thumb Code    44  ffltl.o(.text)
    __aeabi_i2d                              0x08016d81   Thumb Code    34  dflti.o(.text)
    __aeabi_l2d                              0x08016da3   Thumb Code    40  dfltl.o(.text)
    __aeabi_f2d                              0x08016dcb   Thumb Code    38  f2d.o(.text)
    __aeabi_cdcmpeq                          0x08016df1   Thumb Code     0  cdcmple.o(.text)
    __aeabi_cdcmple                          0x08016df1   Thumb Code    48  cdcmple.o(.text)
    __aeabi_cdrcmple                         0x08016e21   Thumb Code    48  cdrcmple.o(.text)
    __aeabi_d2f                              0x08016e51   Thumb Code    56  d2f.o(.text)
    __aeabi_uidiv                            0x08016e89   Thumb Code     0  uidiv.o(.text)
    __aeabi_uidivmod                         0x08016e89   Thumb Code    44  uidiv.o(.text)
    __aeabi_uldivmod                         0x08016eb5   Thumb Code    98  uldiv.o(.text)
    __aeabi_llsl                             0x08016f17   Thumb Code    30  llshl.o(.text)
    _ll_shift_l                              0x08016f17   Thumb Code     0  llshl.o(.text)
    __aeabi_lasr                             0x08016f35   Thumb Code    36  llsshr.o(.text)
    _ll_sshift_r                             0x08016f35   Thumb Code     0  llsshr.o(.text)
    __I$use$fp                               0x08016f59   Thumb Code     0  iusefp.o(.text)
    _float_round                             0x08016f59   Thumb Code    18  fepilogue.o(.text)
    _float_epilogue                          0x08016f6b   Thumb Code    92  fepilogue.o(.text)
    _double_round                            0x08016fc7   Thumb Code    30  depilogue.o(.text)
    _double_epilogue                         0x08016fe5   Thumb Code   156  depilogue.o(.text)
    __aeabi_ddiv                             0x08017081   Thumb Code   222  ddiv.o(.text)
    __aeabi_d2ulz                            0x0801715f   Thumb Code    48  dfixul.o(.text)
    __scatterload                            0x08017191   Thumb Code    28  init.o(.text)
    __scatterload_rt2                        0x08017191   Thumb Code     0  init.o(.text)
    __aeabi_llsr                             0x080171b5   Thumb Code    32  llushr.o(.text)
    _ll_ushift_r                             0x080171b5   Thumb Code     0  llushr.o(.text)
    __decompress                             0x080171d5   Thumb Code     0  __dczerorl2.o(.text)
    __decompress1                            0x080171d5   Thumb Code    86  __dczerorl2.o(.text)
    __0sprintf                               0x0801722d   Thumb Code    34  printfa.o(i.__0sprintf)
    __1sprintf                               0x0801722d   Thumb Code     0  printfa.o(i.__0sprintf)
    __2sprintf                               0x0801722d   Thumb Code     0  printfa.o(i.__0sprintf)
    __c89sprintf                             0x0801722d   Thumb Code     0  printfa.o(i.__0sprintf)
    sprintf                                  0x0801722d   Thumb Code     0  printfa.o(i.__0sprintf)
    __ARM_fpclassifyf                        0x08017255   Thumb Code    38  fpclassifyf.o(i.__ARM_fpclassifyf)
    __hardfp_atan2f                          0x0801727d   Thumb Code   594  atan2f.o(i.__hardfp_atan2f)
    __hardfp_sqrtf                           0x08017529   Thumb Code    58  sqrtf.o(i.__hardfp_sqrtf)
    __mathlib_flt_infnan2                    0x08017563   Thumb Code     6  funder.o(i.__mathlib_flt_infnan2)
    __mathlib_flt_underflow                  0x08017569   Thumb Code    10  funder.o(i.__mathlib_flt_underflow)
    __scatterload_copy                       0x08017579   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x08017587   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x08017589   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    __set_errno                              0x08017599   Thumb Code     6  errno.o(i.__set_errno)
    is_z_position_reached                    0x08017e39   Thumb Code    54  user_task.o(i.is_z_position_reached)
    ASFInfo                                  0x08017e74   Data          24  datatransfer.o(.constdata)
    ASFCNT                                   0x08017e8c   Data           2  datatransfer.o(.constdata)
    _parInfoList                             0x0801a27e   Data         423  anoptv8exapi.o(.constdata)
    _cmdInfoList                             0x0801a425   Data        1350  anoptv8exapi.o(.constdata)
    fast_atan_table                          0x0801a96c   Data        1028  ano_math.o(.constdata)
    Region$$Table$$Base                      0x0801b2d8   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x0801b2f8   Number         0  anon$$obj.o(Region$$Table)
    BEEP_flag                                0x2000005a   Data           1  user_task.o(.data)
    yuyin_flag                               0x2000005b   Data           1  user_task.o(.data)
    mission_step                             0x2000005c   Data           1  user_task.o(.data)
    dadian_cnt                               0x2000005e   Data           1  user_task.o(.data)
    LED_f                                    0x2000005f   Data           1  user_task.o(.data)
    current_patrol_index                     0x20000060   Data           4  user_task.o(.data)
    mission_start_time_ms                    0x20000068   Data           4  user_task.o(.data)
    home_pos                                 0x20000098   Data           8  user_task.o(.data)
    work_pos                                 0x200000c8   Data         756  user_task.o(.data)
    mission_enabled_flag                     0x200003bc   Data           1  user_task.o(.data)
    zigbee_up_f                              0x200003bd   Data           1  user_task.o(.data)
    sti_fun                                  0x200003d6   Data           2  lx_fcstate.o(.data)
    fc_bat                                   0x200003dc   Data           4  lx_lowlevelfunc.o(.data)
    fc_att                                   0x200003e0   Data           7  lx_lowlevelfunc.o(.data)
    fc_vel                                   0x200003e7   Data           6  lx_lowlevelfunc.o(.data)
    Crc32Table                               0x200003f8   Data        1024  crc.o(.data)
    AIRCRAFT_X_PID_KP                        0x20000808   Data           4  pid.o(.data)
    AIRCRAFT_X_PID_KI                        0x2000080c   Data           4  pid.o(.data)
    AIRCRAFT_X_PID_KD                        0x20000810   Data           4  pid.o(.data)
    AIRCRAFT_Y_PID_KP                        0x20000814   Data           4  pid.o(.data)
    AIRCRAFT_Y_PID_KI                        0x20000818   Data           4  pid.o(.data)
    AIRCRAFT_Y_PID_KD                        0x2000081c   Data           4  pid.o(.data)
    AIRCRAFT_Z_PID_KP                        0x20000820   Data           4  pid.o(.data)
    AIRCRAFT_Z_PID_KI                        0x20000824   Data           4  pid.o(.data)
    AIRCRAFT_Z_PID_KD                        0x20000828   Data           4  pid.o(.data)
    AIRCRAFT_YAW_PID_KP                      0x2000082c   Data           4  pid.o(.data)
    AIRCRAFT_YAW_PID_KI                      0x20000830   Data           4  pid.o(.data)
    AIRCRAFT_YAW_PID_KD                      0x20000834   Data           4  pid.o(.data)
    CAM_PID_KP                               0x20000838   Data           4  pid.o(.data)
    CAM_PID_KI                               0x2000083c   Data           4  pid.o(.data)
    CAM_PID_KD                               0x20000840   Data           4  pid.o(.data)
    laser_x_pid_kp                           0x20000844   Data           4  pid.o(.data)
    laser_x_pid_ki                           0x20000848   Data           4  pid.o(.data)
    laser_x_pid_kd                           0x2000084c   Data           4  pid.o(.data)
    laser_y_pid_kp                           0x20000850   Data           4  pid.o(.data)
    laser_y_pid_ki                           0x20000854   Data           4  pid.o(.data)
    laser_y_pid_kd                           0x20000858   Data           4  pid.o(.data)
    laser_reset_request                      0x2000085c   Data           1  pid.o(.data)
    target_pos                               0x2000085e   Data           8  pid.o(.data)
    target_g_port                            0x20000866   Data           4  pid.o(.data)
    target_laser_pixel                       0x2000086a   Data           4  pid.o(.data)
    target_cam                               0x2000086e   Data           4  pid.o(.data)
    visual_offset_x                          0x20000874   Data           4  pid.o(.data)
    visual_offset_y                          0x20000878   Data           4  pid.o(.data)
    flag_visual_pid                          0x2000087c   Data           1  pid.o(.data)
    visual_data_last_update_ms               0x20000880   Data           4  pid.o(.data)
    target_yaw_accumulated                   0x20000884   Data           2  pid.o(.data)
    target_pitch_accumulated                 0x20000886   Data           2  pid.o(.data)
    target_initialized                       0x20000888   Data           1  pid.o(.data)
    filter_initialized                       0x20000889   Data           1  pid.o(.data)
    filtered_yaw                             0x2000088c   Data           4  pid.o(.data)
    filtered_pitch                           0x20000890   Data           4  pid.o(.data)
    error_pos                                0x20000894   Data           8  pid.o(.data)
    error_body                               0x2000089c   Data           4  pid.o(.data)
    error_g_port                             0x200008a0   Data           4  pid.o(.data)
    error_cam                                0x200008a4   Data           4  pid.o(.data)
    error_laser                              0x200008a8   Data           4  pid.o(.data)
    cam_V                                    0x200008ac   Data           8  pid.o(.data)
    g_port_V                                 0x200008b4   Data           4  pid.o(.data)
    laser_V                                  0x200008b8   Data           4  pid.o(.data)
    flag_cam                                 0x200008bc   Data           1  pid.o(.data)
    flag_Control                             0x200008bd   Data           4  pid.o(.data)
    flag_g_port                              0x200008c1   Data           1  pid.o(.data)
    yaw_turn_flag                            0x200008c2   Data           1  pid.o(.data)
    flag_laser                               0x200008c3   Data           1  pid.o(.data)
    screen                                   0x200008e0   Data           3  zigbee.o(.data)
    swjLinkType                              0x20000910   Data           2  anoptv8exapi.o(.data)
    testPar_u8                               0x20000912   Data           1  anoptv8exapi.o(.data)
    testPar_s16                              0x20000914   Data           2  anoptv8exapi.o(.data)
    _strParTest                              0x20000916   Data          30  anoptv8exapi.o(.data)
    cmdCount                                 0x20000938   Data           4  anoptv8cmd.o(.data)
    parCount                                 0x20000944   Data           4  anoptv8par.o(.data)
    EmergencyStopESC                         0x20000948   Data           1  drv_bsp.o(.data)
    U1TxInCnt                                0x20000968   Data           2  drv_uart.o(.data)
    U1TxOutCnt                               0x2000096a   Data           2  drv_uart.o(.data)
    U1RxInCnt                                0x2000096c   Data           2  drv_uart.o(.data)
    U1RxoutCnt                               0x2000096e   Data           2  drv_uart.o(.data)
    U2TxInCnt                                0x20000970   Data           2  drv_uart.o(.data)
    U2TxOutCnt                               0x20000972   Data           2  drv_uart.o(.data)
    U2RxInCnt                                0x20000974   Data           2  drv_uart.o(.data)
    U2RxoutCnt                               0x20000976   Data           2  drv_uart.o(.data)
    U3TxInCnt                                0x20000978   Data           2  drv_uart.o(.data)
    U3TxOutCnt                               0x2000097a   Data           2  drv_uart.o(.data)
    U3RxInCnt                                0x2000097c   Data           2  drv_uart.o(.data)
    U3RxoutCnt                               0x2000097e   Data           2  drv_uart.o(.data)
    U4TxInCnt                                0x20000980   Data           2  drv_uart.o(.data)
    U4TxOutCnt                               0x20000982   Data           2  drv_uart.o(.data)
    U4RxInCnt                                0x20000984   Data           2  drv_uart.o(.data)
    U4RxoutCnt                               0x20000986   Data           2  drv_uart.o(.data)
    U5TxInCnt                                0x20000988   Data           2  drv_uart.o(.data)
    U5TxOutCnt                               0x2000098a   Data           2  drv_uart.o(.data)
    U5RxInCnt                                0x2000098c   Data           2  drv_uart.o(.data)
    U5RxoutCnt                               0x2000098e   Data           2  drv_uart.o(.data)
    U7TxInCnt                                0x20000990   Data           2  drv_uart.o(.data)
    U7TxOutCnt                               0x20000992   Data           2  drv_uart.o(.data)
    U7RxInCnt                                0x20000994   Data           2  drv_uart.o(.data)
    U7RxoutCnt                               0x20000996   Data           2  drv_uart.o(.data)
    U8TxInCnt                                0x20000998   Data           2  drv_uart.o(.data)
    U8TxOutCnt                               0x2000099a   Data           2  drv_uart.o(.data)
    U8RxInCnt                                0x2000099c   Data           2  drv_uart.o(.data)
    U8RxoutCnt                               0x2000099e   Data           2  drv_uart.o(.data)
    ep_tx_busy_flag                          0x200009a8   Data           1  drv_usb.o(.data)
    cdc_out_ep1                              0x200009ac   Data           8  drv_usb.o(.data)
    cdc_in_ep1                               0x200009b4   Data           8  drv_usb.o(.data)
    CdnTxDataBufInIndex                      0x200009bc   Data           2  drv_usb.o(.data)
    CdnTxDataBufOutIndex                     0x200009be   Data           2  drv_usb.o(.data)
    AdcVal_Bat                               0x200009c4   Data           4  drv_adc.o(.data)
    SystemCoreClock                          0x200009c8   Data           4  system_stm32f4xx.o(.data)
    AHBPrescTable                            0x200009cc   Data          16  system_stm32f4xx.o(.data)
    usbd_intf_head                           0x200009ec   Data           4  usbd_core.o(.data)
    cdcReadBuf                               0x20000a00   Data         128  drv_usb.o(.noncacheable)
    cdcSendBuf                               0x20000a80   Data         128  drv_usb.o(.noncacheable)
    g_dwc2_udc                               0x20000b00   Data         192  usb_dc_dwc2.o(.noncacheable)
    usbd_core_cfg                            0x20000bc0   Data         288  usbd_core.o(.noncacheable)
    actual_work_pos                          0x20001292   Data         504  user_task.o(.bss)
    ext_sens                                 0x200014a2   Data          48  lx_extsensor.o(.bss)
    fc_sta                                   0x200014d2   Data          11  lx_fcstate.o(.bss)
    rt_tar                                   0x200014de   Data          14  lx_lowlevelfunc.o(.bss)
    pwm_to_esc                               0x200014ec   Data          16  lx_lowlevelfunc.o(.bss)
    fc_att_qua                               0x200014fc   Data           9  lx_lowlevelfunc.o(.bss)
    fc_alt                                   0x20001505   Data           9  lx_lowlevelfunc.o(.bss)
    gimbal_angle_data                        0x2000151e   Data          24  g_port.o(.bss)
    mid360                                   0x20001590   Data          48  mid360.o(.bss)
    float_get                                0x200015c0   Data          12  mid360.o(.bss)
    DataReceive                              0x200015cc   Data          12  mid360.o(.bss)
    PID_V                                    0x200015fc   Data          16  pid.o(.bss)
    X_PID                                    0x2000160c   Data          88  pid.o(.bss)
    Y_PID                                    0x20001664   Data          88  pid.o(.bss)
    Z_PID                                    0x200016bc   Data          88  pid.o(.bss)
    Yaw_PID                                  0x20001714   Data          88  pid.o(.bss)
    cam_PID                                  0x2000176c   Data          88  pid.o(.bss)
    g_port_PID                               0x200017c4   Data          88  pid.o(.bss)
    laser_x_PID                              0x2000181c   Data          88  pid.o(.bss)
    laser_y_PID                              0x20001874   Data          88  pid.o(.bss)
    maixcam                                  0x2000190c   Data          13  maixcam.o(.bss)
    g_tfmini_sensor                          0x2000193c   Data          44  tofmini.o(.bss)
    recvBuf                                  0x20001968   Data        1696  anoptv8run.o(.bss)
    AnoPTv8TxBuf                             0x20002008   Data        4320  anoptv8run.o(.bss)
    pCmdInfoList                             0x200030e8   Data         512  anoptv8cmd.o(.bss)
    pParInfoList                             0x200032e8   Data        2048  anoptv8par.o(.bss)
    rc_in                                    0x20003ae8   Data         116  drv_bsp.o(.bss)
    sbus_in                                  0x20003b5c   Data          25  drv_bsp.o(.bss)
    ano_of                                   0x20003b90   Data          60  drv_anoof.o(.bss)
    U1TxBuf                                  0x20003bd8   Data        2048  drv_uart.o(.bss)
    U1RxBuf                                  0x200043d8   Data         256  drv_uart.o(.bss)
    U2TxBuf                                  0x200044d8   Data        2048  drv_uart.o(.bss)
    U2RxBuf                                  0x20004cd8   Data         256  drv_uart.o(.bss)
    U3TxBuf                                  0x20004dd8   Data         256  drv_uart.o(.bss)
    U3RxBuf                                  0x20004ed8   Data         256  drv_uart.o(.bss)
    U4TxBuf                                  0x20004fd8   Data         256  drv_uart.o(.bss)
    U4RxBuf                                  0x200050d8   Data         256  drv_uart.o(.bss)
    U5TxBuf                                  0x200051d8   Data         256  drv_uart.o(.bss)
    U5RxBuf                                  0x200052d8   Data         256  drv_uart.o(.bss)
    U7TxBuf                                  0x200053d8   Data         256  drv_uart.o(.bss)
    U7RxBuf                                  0x200054d8   Data         256  drv_uart.o(.bss)
    U8TxBuf                                  0x200055d8   Data         256  drv_uart.o(.bss)
    U8RxBuf                                  0x200056d8   Data         256  drv_uart.o(.bss)
    crsf_frame                               0x200057d8   Data          68  drv_rcin.o(.bss)
    intf0                                    0x20005860   Data          32  drv_usb.o(.bss)
    intf1                                    0x20005880   Data          32  drv_usb.o(.bss)
    CdcTxDataBuf                             0x200058a0   Data        1024  drv_usb.o(.bss)
    adcBuffer                                0x20005ca0   Data          16  drv_adc.o(.bss)
    tx_msg                                   0x20005cb0   Data          96  usbd_core.o(.bss)
    rx_msg                                   0x20005d10   Data          96  usbd_core.o(.bss)
    __initial_sp                             0x20006170   Data           0  startup_stm32f429_439xx.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x080001ad

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x0001bfd8, Max: 0x00100000, ABSOLUTE, COMPRESSED[0x0001b8f8])

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x0001b2f8, Max: 0x00100000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x000001ac   Data   RO         1827    RESET               startup_stm32f429_439xx.o
    0x080001ac   0x080001ac   0x00000000   Code   RO         1921  * .ARM.Collect$$$$00000000  mc_w.l(entry.o)
    0x080001ac   0x080001ac   0x00000004   Code   RO         2229    .ARM.Collect$$$$00000001  mc_w.l(entry2.o)
    0x080001b0   0x080001b0   0x00000004   Code   RO         2232    .ARM.Collect$$$$00000004  mc_w.l(entry5.o)
    0x080001b4   0x080001b4   0x00000000   Code   RO         2234    .ARM.Collect$$$$00000008  mc_w.l(entry7b.o)
    0x080001b4   0x080001b4   0x00000000   Code   RO         2236    .ARM.Collect$$$$0000000A  mc_w.l(entry8b.o)
    0x080001b4   0x080001b4   0x00000008   Code   RO         2237    .ARM.Collect$$$$0000000B  mc_w.l(entry9a.o)
    0x080001bc   0x080001bc   0x00000004   Code   RO         2244    .ARM.Collect$$$$0000000E  mc_w.l(entry12b.o)
    0x080001c0   0x080001c0   0x00000000   Code   RO         2239    .ARM.Collect$$$$0000000F  mc_w.l(entry10a.o)
    0x080001c0   0x080001c0   0x00000000   Code   RO         2241    .ARM.Collect$$$$00000011  mc_w.l(entry11a.o)
    0x080001c0   0x080001c0   0x00000004   Code   RO         2230    .ARM.Collect$$$$00002712  mc_w.l(entry2.o)
    0x080001c4   0x080001c4   0x00000014   Code   RO            4    .text               main.o
    0x080001d8   0x080001d8   0x00000100   Code   RO          122    .text               ano_scheduler.o
    0x080002d8   0x080002d8   0x000029a0   Code   RO          206    .text               user_task.o
    0x08002c78   0x08002c78   0x0000043c   Code   RO          393    .text               datatransfer.o
    0x080030b4   0x080030b4   0x00000074   Code   RO          459    .text               lx_extsensor.o
    0x08003128   0x08003128   0x00000214   Code   RO          493    .text               lx_fcfunc.o
    0x0800333c   0x0800333c   0x00000284   Code   RO          521    .text               lx_fcstate.o
    0x080035c0   0x080035c0   0x00000488   Code   RO          552    .text               lx_lowlevelfunc.o
    0x08003a48   0x08003a48   0x00000038   Code   RO          605    .text               crc.o
    0x08003a80   0x08003a80   0x00000624   Code   RO          635    .text               g_port.o
    0x080040a4   0x080040a4   0x000001ec   Code   RO          712    .text               mid360.o
    0x08004290   0x08004290   0x000014e4   Code   RO          746    .text               pid.o
    0x08005774   0x08005774   0x00000f6c   Code   RO          804    .text               zigbee.o
    0x080066e0   0x080066e0   0x0000016c   Code   RO          839    .text               maixcam.o
    0x0800684c   0x0800684c   0x000003c8   Code   RO          901    .text               tofmini.o
    0x08006c14   0x08006c14   0x00000234   Code   RO          934    .text               path_storage.o
    0x08006e48   0x08006e48   0x00000ef0   Code   RO          964    .text               anoptv8exapi.o
    0x08007d38   0x08007d38   0x000003b0   Code   RO         1006    .text               anoptv8run.o
    0x080080e8   0x080080e8   0x000003dc   Code   RO         1025    .text               anoptv8cmd.o
    0x080084c4   0x080084c4   0x00000638   Code   RO         1041    .text               anoptv8par.o
    0x08008afc   0x08008afc   0x00000e70   Code   RO         1057    .text               anoptv8framefactory.o
    0x0800996c   0x0800996c   0x00000518   Code   RO         1072    .text               drv_bsp.o
    0x08009e84   0x08009e84   0x000009a4   Code   RO         1109    .text               ano_math.o
    0x0800a828   0x0800a828   0x000001f8   Code   RO         1136    .text               drv_anoof.o
    0x0800aa20   0x0800aa20   0x0000008c   Code   RO         1221    .text               stm32f4xx_it.o
    0x0800aaac   0x0800aaac   0x00000168   Code   RO         1245    .text               drv_sys.o
    0x0800ac14   0x0800ac14   0x00000044   Code   RO         1281    .text               drv_led.o
    0x0800ac58   0x0800ac58   0x0000008c   Code   RO         1305    .text               drv_timer.o
    0x0800ace4   0x0800ace4   0x00000f28   Code   RO         1332    .text               drv_uart.o
    0x0800bc0c   0x0800bc0c   0x00000370   Code   RO         1363    .text               drv_pwmout.o
    0x0800bf7c   0x0800bf7c   0x000003c0   Code   RO         1413    .text               drv_rcin.o
    0x0800c33c   0x0800c33c   0x0000029c   Code   RO         1444    .text               drv_usb.o
    0x0800c5d8   0x0800c5d8   0x000001c4   Code   RO         1515    .text               drv_adc.o
    0x0800c79c   0x0800c79c   0x00000244   Code   RO         1546    .text               system_stm32f4xx.o
    0x0800c9e0   0x0800c9e0   0x000001c0   Code   RO         1576    .text               misc.o
    0x0800cba0   0x0800cba0   0x00000fdc   Code   RO         1601    .text               stm32f4xx_adc.o
    0x0800db7c   0x0800db7c   0x00001368   Code   RO         1626    .text               stm32f4xx_dma.o
    0x0800eee4   0x0800eee4   0x000002d4   Code   RO         1651    .text               stm32f4xx_exti.o
    0x0800f1b8   0x0800f1b8   0x000009d8   Code   RO         1701    .text               stm32f4xx_gpio.o
    0x0800fb90   0x0800fb90   0x00000ef0   Code   RO         1726    .text               stm32f4xx_rcc.o
    0x08010a80   0x08010a80   0x000031ec   Code   RO         1778    .text               stm32f4xx_tim.o
    0x08013c6c   0x08013c6c   0x000010e4   Code   RO         1803    .text               stm32f4xx_usart.o
    0x08014d50   0x08014d50   0x00000024   Code   RO         1828    .text               startup_stm32f429_439xx.o
    0x08014d74   0x08014d74   0x00001208   Code   RO         1832    .text               usb_dc_dwc2.o
    0x08015f7c   0x08015f7c   0x000000c4   Code   RO         1858    .text               usbd_cdc.o
    0x08016040   0x08016040   0x00000a68   Code   RO         1874    .text               usbd_core.o
    0x08016aa8   0x08016aa8   0x00000024   Code   RO         1924    .text               mc_w.l(memcpya.o)
    0x08016acc   0x08016acc   0x00000024   Code   RO         1926    .text               mc_w.l(memseta.o)
    0x08016af0   0x08016af0   0x00000018   Code   RO         1928    .text               mc_w.l(strcat.o)
    0x08016b08   0x08016b08   0x0000001a   Code   RO         1930    .text               mc_w.l(memcmp.o)
    0x08016b22   0x08016b22   0x0000014e   Code   RO         2195    .text               mf_w.l(dadd.o)
    0x08016c70   0x08016c70   0x000000e4   Code   RO         2197    .text               mf_w.l(dmul.o)
    0x08016d54   0x08016d54   0x0000002c   Code   RO         2199    .text               mf_w.l(ffltl.o)
    0x08016d80   0x08016d80   0x00000022   Code   RO         2201    .text               mf_w.l(dflti.o)
    0x08016da2   0x08016da2   0x00000028   Code   RO         2203    .text               mf_w.l(dfltl.o)
    0x08016dca   0x08016dca   0x00000026   Code   RO         2205    .text               mf_w.l(f2d.o)
    0x08016df0   0x08016df0   0x00000030   Code   RO         2207    .text               mf_w.l(cdcmple.o)
    0x08016e20   0x08016e20   0x00000030   Code   RO         2209    .text               mf_w.l(cdrcmple.o)
    0x08016e50   0x08016e50   0x00000038   Code   RO         2211    .text               mf_w.l(d2f.o)
    0x08016e88   0x08016e88   0x0000002c   Code   RO         2248    .text               mc_w.l(uidiv.o)
    0x08016eb4   0x08016eb4   0x00000062   Code   RO         2250    .text               mc_w.l(uldiv.o)
    0x08016f16   0x08016f16   0x0000001e   Code   RO         2252    .text               mc_w.l(llshl.o)
    0x08016f34   0x08016f34   0x00000024   Code   RO         2254    .text               mc_w.l(llsshr.o)
    0x08016f58   0x08016f58   0x00000000   Code   RO         2263    .text               mc_w.l(iusefp.o)
    0x08016f58   0x08016f58   0x0000006e   Code   RO         2264    .text               mf_w.l(fepilogue.o)
    0x08016fc6   0x08016fc6   0x000000ba   Code   RO         2266    .text               mf_w.l(depilogue.o)
    0x08017080   0x08017080   0x000000de   Code   RO         2268    .text               mf_w.l(ddiv.o)
    0x0801715e   0x0801715e   0x00000030   Code   RO         2270    .text               mf_w.l(dfixul.o)
    0x0801718e   0x0801718e   0x00000002   PAD
    0x08017190   0x08017190   0x00000024   Code   RO         2272    .text               mc_w.l(init.o)
    0x080171b4   0x080171b4   0x00000020   Code   RO         2275    .text               mc_w.l(llushr.o)
    0x080171d4   0x080171d4   0x00000056   Code   RO         2286    .text               mc_w.l(__dczerorl2.o)
    0x0801722a   0x0801722a   0x00000002   PAD
    0x0801722c   0x0801722c   0x00000028   Code   RO         2169    i.__0sprintf        mc_w.l(printfa.o)
    0x08017254   0x08017254   0x00000026   Code   RO         2213    i.__ARM_fpclassifyf  m_wm.l(fpclassifyf.o)
    0x0801727a   0x0801727a   0x00000002   PAD
    0x0801727c   0x0801727c   0x000002ac   Code   RO         1897    i.__hardfp_atan2f   m_wm.l(atan2f.o)
    0x08017528   0x08017528   0x0000003a   Code   RO         1909    i.__hardfp_sqrtf    m_wm.l(sqrtf.o)
    0x08017562   0x08017562   0x00000006   Code   RO         2217    i.__mathlib_flt_infnan2  m_wm.l(funder.o)
    0x08017568   0x08017568   0x00000010   Code   RO         2221    i.__mathlib_flt_underflow  m_wm.l(funder.o)
    0x08017578   0x08017578   0x0000000e   Code   RO         2280    i.__scatterload_copy  mc_w.l(handlers.o)
    0x08017586   0x08017586   0x00000002   Code   RO         2281    i.__scatterload_null  mc_w.l(handlers.o)
    0x08017588   0x08017588   0x0000000e   Code   RO         2282    i.__scatterload_zeroinit  mc_w.l(handlers.o)
    0x08017596   0x08017596   0x00000002   PAD
    0x08017598   0x08017598   0x0000000c   Code   RO         2258    i.__set_errno       mc_w.l(errno.o)
    0x080175a4   0x080175a4   0x00000184   Code   RO         2174    i._fp_digits        mc_w.l(printfa.o)
    0x08017728   0x08017728   0x000006b4   Code   RO         2175    i._printf_core      mc_w.l(printfa.o)
    0x08017ddc   0x08017ddc   0x00000024   Code   RO         2176    i._printf_post_padding  mc_w.l(printfa.o)
    0x08017e00   0x08017e00   0x0000002e   Code   RO         2177    i._printf_pre_padding  mc_w.l(printfa.o)
    0x08017e2e   0x08017e2e   0x0000000a   Code   RO         2179    i._sputc            mc_w.l(printfa.o)
    0x08017e38   0x08017e38   0x0000003c   Code   RO          229    i.is_z_position_reached  user_task.o
    0x08017e74   0x08017e74   0x0000001a   Data   RO          395    .constdata          datatransfer.o
    0x08017e8e   0x08017e8e   0x000023f0   Data   RO          935    .constdata          path_storage.o
    0x0801a27e   0x0801a27e   0x000006ed   Data   RO          965    .constdata          anoptv8exapi.o
    0x0801a96b   0x0801a96b   0x00000001   PAD
    0x0801a96c   0x0801a96c   0x00000404   Data   RO         1110    .constdata          ano_math.o
    0x0801ad70   0x0801ad70   0x00000094   Data   RO         1446    .constdata          drv_usb.o
    0x0801ae04   0x0801ae04   0x000001b2   Data   RO          208    .conststring        user_task.o
    0x0801afb6   0x0801afb6   0x00000002   PAD
    0x0801afb8   0x0801afb8   0x0000008c   Data   RO          936    .conststring        path_storage.o
    0x0801b044   0x0801b044   0x00000048   Data   RO         1577    .conststring        misc.o
    0x0801b08c   0x0801b08c   0x00000051   Data   RO         1602    .conststring        stm32f4xx_adc.o
    0x0801b0dd   0x0801b0dd   0x00000003   PAD
    0x0801b0e0   0x0801b0e0   0x00000051   Data   RO         1627    .conststring        stm32f4xx_dma.o
    0x0801b131   0x0801b131   0x00000003   PAD
    0x0801b134   0x0801b134   0x00000052   Data   RO         1652    .conststring        stm32f4xx_exti.o
    0x0801b186   0x0801b186   0x00000002   PAD
    0x0801b188   0x0801b188   0x00000052   Data   RO         1702    .conststring        stm32f4xx_gpio.o
    0x0801b1da   0x0801b1da   0x00000002   PAD
    0x0801b1dc   0x0801b1dc   0x00000051   Data   RO         1727    .conststring        stm32f4xx_rcc.o
    0x0801b22d   0x0801b22d   0x00000003   PAD
    0x0801b230   0x0801b230   0x00000051   Data   RO         1779    .conststring        stm32f4xx_tim.o
    0x0801b281   0x0801b281   0x00000003   PAD
    0x0801b284   0x0801b284   0x00000053   Data   RO         1804    .conststring        stm32f4xx_usart.o
    0x0801b2d7   0x0801b2d7   0x00000001   PAD
    0x0801b2d8   0x0801b2d8   0x00000020   Data   RO         2278    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x0801b2f8, Size: 0x00006170, Max: 0x00030000, ABSOLUTE, COMPRESSED[0x00000600])

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   COMPRESSED   0x00000055   Data   RW          123    .data               ano_scheduler.o
    0x20000055   COMPRESSED   0x00000003   PAD
    0x20000058   COMPRESSED   0x0000037c   Data   RW          209    .data               user_task.o
    0x200003d4   COMPRESSED   0x00000001   Data   RW          396    .data               datatransfer.o
    0x200003d5   COMPRESSED   0x00000001   Data   RW          494    .data               lx_fcfunc.o
    0x200003d6   COMPRESSED   0x00000006   Data   RW          523    .data               lx_fcstate.o
    0x200003dc   COMPRESSED   0x0000001c   Data   RW          554    .data               lx_lowlevelfunc.o
    0x200003f8   COMPRESSED   0x00000400   Data   RW          606    .data               crc.o
    0x200007f8   COMPRESSED   0x00000009   Data   RW          637    .data               g_port.o
    0x20000801   COMPRESSED   0x00000001   PAD
    0x20000802   COMPRESSED   0x00000005   Data   RW          714    .data               mid360.o
    0x20000807   COMPRESSED   0x00000001   PAD
    0x20000808   COMPRESSED   0x000000d8   Data   RW          748    .data               pid.o
    0x200008e0   COMPRESSED   0x00000027   Data   RW          806    .data               zigbee.o
    0x20000907   COMPRESSED   0x00000008   Data   RW          841    .data               maixcam.o
    0x2000090f   COMPRESSED   0x00000001   PAD
    0x20000910   COMPRESSED   0x00000024   Data   RW          966    .data               anoptv8exapi.o
    0x20000934   COMPRESSED   0x00000002   Data   RW         1008    .data               anoptv8run.o
    0x20000936   COMPRESSED   0x00000002   PAD
    0x20000938   COMPRESSED   0x0000000a   Data   RW         1027    .data               anoptv8cmd.o
    0x20000942   COMPRESSED   0x00000002   PAD
    0x20000944   COMPRESSED   0x00000004   Data   RW         1043    .data               anoptv8par.o
    0x20000948   COMPRESSED   0x00000013   Data   RW         1074    .data               drv_bsp.o
    0x2000095b   COMPRESSED   0x00000005   PAD
    0x20000960   COMPRESSED   0x00000008   Data   RW         1246    .data               drv_sys.o
    0x20000968   COMPRESSED   0x00000038   Data   RW         1334    .data               drv_uart.o
    0x200009a0   COMPRESSED   0x00000005   Data   RW         1415    .data               drv_rcin.o
    0x200009a5   COMPRESSED   0x00000003   PAD
    0x200009a8   COMPRESSED   0x00000019   Data   RW         1447    .data               drv_usb.o
    0x200009c1   COMPRESSED   0x00000003   PAD
    0x200009c4   COMPRESSED   0x00000004   Data   RW         1517    .data               drv_adc.o
    0x200009c8   COMPRESSED   0x00000014   Data   RW         1547    .data               system_stm32f4xx.o
    0x200009dc   COMPRESSED   0x00000010   Data   RW         1728    .data               stm32f4xx_rcc.o
    0x200009ec   COMPRESSED   0x00000010   Data   RW         1876    .data               usbd_core.o
    0x200009fc   COMPRESSED   0x00000004   Data   RW         2259    .data               mc_w.l(errno.o)
    0x20000a00   COMPRESSED   0x00000100   Data   RW         1448    .noncacheable       drv_usb.o
    0x20000b00   COMPRESSED   0x000000c0   Data   RW         1833    .noncacheable       usb_dc_dwc2.o
    0x20000bc0   COMPRESSED   0x00000120   Data   RW         1877    .noncacheable       usbd_core.o
    0x20000ce0        -       0x000007aa   Zero   RW          207    .bss                user_task.o
    0x2000148a        -       0x00000018   Zero   RW          394    .bss                datatransfer.o
    0x200014a2        -       0x00000030   Zero   RW          460    .bss                lx_extsensor.o
    0x200014d2        -       0x0000000b   Zero   RW          522    .bss                lx_fcstate.o
    0x200014dd   COMPRESSED   0x00000001   PAD
    0x200014de        -       0x00000040   Zero   RW          553    .bss                lx_lowlevelfunc.o
    0x2000151e        -       0x00000070   Zero   RW          636    .bss                g_port.o
    0x2000158e   COMPRESSED   0x00000002   PAD
    0x20001590        -       0x0000006c   Zero   RW          713    .bss                mid360.o
    0x200015fc        -       0x000002d0   Zero   RW          747    .bss                pid.o
    0x200018cc        -       0x00000040   Zero   RW          805    .bss                zigbee.o
    0x2000190c        -       0x0000000d   Zero   RW          840    .bss                maixcam.o
    0x20001919   COMPRESSED   0x00000003   PAD
    0x2000191c        -       0x0000004c   Zero   RW          902    .bss                tofmini.o
    0x20001968        -       0x00001780   Zero   RW         1007    .bss                anoptv8run.o
    0x200030e8        -       0x00000200   Zero   RW         1026    .bss                anoptv8cmd.o
    0x200032e8        -       0x00000800   Zero   RW         1042    .bss                anoptv8par.o
    0x20003ae8        -       0x000000a6   Zero   RW         1073    .bss                drv_bsp.o
    0x20003b8e   COMPRESSED   0x00000002   PAD
    0x20003b90        -       0x00000048   Zero   RW         1137    .bss                drv_anoof.o
    0x20003bd8        -       0x00001c00   Zero   RW         1333    .bss                drv_uart.o
    0x200057d8        -       0x00000088   Zero   RW         1414    .bss                drv_rcin.o
    0x20005860        -       0x00000440   Zero   RW         1445    .bss                drv_usb.o
    0x20005ca0        -       0x00000010   Zero   RW         1516    .bss                drv_adc.o
    0x20005cb0        -       0x000000c0   Zero   RW         1875    .bss                usbd_core.o
    0x20005d70        -       0x00000400   Zero   RW         1825    STACK               startup_stm32f429_439xx.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

      2468        152       1028          0          0       8452   ano_math.o
       256         14          0         85          0       5580   ano_scheduler.o
       988         60          0         10        512       4587   anoptv8cmd.o
      3824        958       1773         36          0      10720   anoptv8exapi.o
      3696         30          0          0          0       7484   anoptv8framefactory.o
      1592         42          0          4       2048       4260   anoptv8par.o
       944         14          0          2       6016       3865   anoptv8run.o
        56          4          0       1024          0       1286   crc.o
      1084         72         26          1         24       4365   datatransfer.o
       452         38          0          4         16       1810   drv_adc.o
       504         22          0          0         72       2711   drv_anoof.o
      1304         74          0         19        166       4551   drv_bsp.o
        68          6          0          0          0        609   drv_led.o
       880         42          0          0          0       1895   drv_pwmout.o
       960         38          0          5        136       5130   drv_rcin.o
       360         18          0          8          0      29115   drv_sys.o
       140          4          0          0          0        955   drv_timer.o
      3880        232          0         56       7168      12673   drv_uart.o
       668         58        148        281       1088      61627   drv_usb.o
      1572         66          0          9        112     249110   g_port.o
       116         10          0          0         48       2539   lx_extsensor.o
       532         54          0          1          0       3156   lx_fcfunc.o
       644         30          0          6         11       2111   lx_fcstate.o
      1160         90          0         28         64       3649   lx_lowlevelfunc.o
        20          0          0          0          0     331432   main.o
       364         18          0          8         13       3572   maixcam.o
       492         34          0          5        108       3993   mid360.o
       448         30         72          0          0       2345   misc.o
       564        256       9340          0          0       2789   path_storage.o
      5348        542          0        216        720      15318   pid.o
        36          8        428          0       1024        988   startup_stm32f429_439xx.o
      4060        104         81          0          0      13662   stm32f4xx_adc.o
      4968        316         81          0          0       9269   stm32f4xx_dma.o
       724         14         82          0          0       2999   stm32f4xx_exti.o
      2520        136         82          0          0       5881   stm32f4xx_gpio.o
       140          4          0          0          0       2604   stm32f4xx_it.o
      3824        230         81         16          0      16412   stm32f4xx_rcc.o
     12780        628         81          0          0      31512   stm32f4xx_tim.o
      4324        204         83          0          0      10812   stm32f4xx_usart.o
       580         46          0         20          0       2331   system_stm32f4xx.o
       968         20          0          0         76       7439   tofmini.o
      4616        136          0        192          0      17395   usb_dc_dwc2.o
       196          8          0          0          0       2581   usbd_cdc.o
      2664        108          0        304        192      18621   usbd_core.o
     10716       3506        434        892       1962     268719   user_task.o
      3948       1294          0         39         64      10093   zigbee.o

    ----------------------------------------------------------------------
     92448       <USER>      <GROUP>       3292      21648    1213007   Object Totals
         0          0         32          0          0          0   (incl. Generated)
         0          0         20         21          8          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

       684         90          0          0          0        208   atan2f.o
        38          0          0          0          0        116   fpclassifyf.o
        22          6          0          0          0        232   funder.o
        58          0          0          0          0        136   sqrtf.o
        86          0          0          0          0          0   __dczerorl2.o
         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         4          0          0          0          0          0   entry12b.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        12          6          0          4          0         68   errno.o
        30          0          0          0          0          0   handlers.o
        36          8          0          0          0         68   init.o
         0          0          0          0          0          0   iusefp.o
        30          0          0          0          0         68   llshl.o
        36          0          0          0          0         68   llsshr.o
        32          0          0          0          0         68   llushr.o
        26          0          0          0          0         80   memcmp.o
        36          0          0          0          0         68   memcpya.o
        36          0          0          0          0        108   memseta.o
      2236         86          0          0          0        532   printfa.o
        24          0          0          0          0         68   strcat.o
        44          0          0          0          0         80   uidiv.o
        98          0          0          0          0         92   uldiv.o
        48          0          0          0          0         68   cdcmple.o
        48          0          0          0          0         68   cdrcmple.o
        56          0          0          0          0         88   d2f.o
       334          0          0          0          0        148   dadd.o
       222          0          0          0          0        100   ddiv.o
       186          0          0          0          0        176   depilogue.o
        48          0          0          0          0         68   dfixul.o
        34          0          0          0          0         76   dflti.o
        40          0          0          0          0         80   dfltl.o
       228          0          0          0          0         96   dmul.o
        38          0          0          0          0         68   f2d.o
       110          0          0          0          0        168   fepilogue.o
        44          0          0          0          0         92   ffltl.o

    ----------------------------------------------------------------------
      5032        <USER>          <GROUP>          4          0       3356   Library Totals
         8          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

       802         96          0          0          0        692   m_wm.l
      2786        108          0          4          0       1368   mc_w.l
      1436          0          0          0          0       1296   mf_w.l

    ----------------------------------------------------------------------
      5032        <USER>          <GROUP>          4          0       3356   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     97480       9974      13872       3296      21648    1205043   Grand Totals
     97480       9974      13872       1536      21648    1205043   ELF Image Totals (compressed)
     97480       9974      13872       1536          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)               111352 ( 108.74kB)
    Total RW  Size (RW Data + ZI Data)             24944 (  24.36kB)
    Total ROM Size (Code + RO Data + RW Data)     112888 ( 110.24kB)

==============================================================================


#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
C代码生成器扩展测试
版权信息：米醋电子工作室
创建日期：2025-07-31

功能描述：
测试扩展后的C代码生成器，验证返航路径功能的正确性
"""

import json
import os
import tempfile
from generate_c_code import CCodeGenerator

def create_test_data():
    """创建测试用的路径数据"""
    test_data = {
        "metadata": {
            "generation_time": "2025-07-31 12:00:00",
            "validation_report": {
                "success_rate": 100.0
            }
        },
        "path_data": [
            {
                "no_fly_zones": [11, 21, 31],
                "path_sequence": [91, 81, 71, 61, 51, 41, 42, 32, 22, 12],
                "path_length": 10,
                "return_path": [12, 22, 32, 42, 52, 62, 72, 82, 91],
                "return_length": 9,
                "computation_time_ms": 15.5,
                "total_distance": 125.8,
                "coverage_rate": 95.2,
                "is_valid": True
            },
            {
                "no_fly_zones": [33, 34, 35],
                "path_sequence": [91, 81, 71, 61, 51, 41, 43, 53, 63, 73],
                "path_length": 10,
                "return_path": [73, 63, 53, 43, 33, 23, 13, 14, 24, 34, 44, 54, 64, 74, 84, 94, 93, 92, 91],
                "return_length": 19,
                "computation_time_ms": 18.2,
                "total_distance": 142.3,
                "coverage_rate": 92.8,
                "is_valid": True
            },
            {
                "no_fly_zones": [55, 56, 57],
                "path_sequence": [91, 81, 71, 61, 51, 41, 31, 21, 11, 12],
                "path_length": 10,
                "return_path": [12, 13, 23, 33, 43, 53, 63, 73, 83, 93, 92, 91],
                "return_length": 12,
                "computation_time_ms": 12.8,
                "total_distance": 118.5,
                "coverage_rate": 97.1,
                "is_valid": True
            }
        ]
    }
    return test_data

def test_data_structure_expansion():
    """测试数据结构扩展"""
    print("🧪 测试数据结构扩展")
    print("=" * 50)
    
    generator = CCodeGenerator()
    test_data = create_test_data()
    
    # 生成头文件
    header_content = generator.generate_header_file(len(test_data['path_data']))
    
    # 验证常量定义
    checks = [
        ("MAX_RETURN_LENGTH", "25"),
        ("precomputed_path_t", "return_length"),
        ("precomputed_path_t", "return_sequence"),
        ("find_precomputed_return_path", "函数声明"),
        ("find_precomputed_return_path_direct", "函数声明")
    ]
    
    for check_item, description in checks:
        if check_item in header_content:
            print(f"   ✅ {description}: 找到 {check_item}")
        else:
            print(f"   ❌ {description}: 未找到 {check_item}")

def test_source_generation():
    """测试源文件生成"""
    print("\n🔧 测试源文件生成")
    print("=" * 50)
    
    generator = CCodeGenerator()
    test_data = create_test_data()
    
    # 生成源文件
    source_content = generator.generate_source_file(test_data['path_data'])
    
    # 验证源文件内容
    checks = [
        ("return_length", "返航路径长度字段"),
        ("return_sequence", "返航路径序列字段"),
        ("find_precomputed_return_path", "返航路径查找函数"),
        ("find_precomputed_return_path_direct", "直接返航路径查找函数"),
        ("Found return path pointer", "调试输出信息")
    ]
    
    for check_item, description in checks:
        if check_item in source_content:
            print(f"   ✅ {description}: 找到 {check_item}")
        else:
            print(f"   ❌ {description}: 未找到 {check_item}")
    
    # 验证数据格式
    path_count = source_content.count("// 路径")
    print(f"   📊 生成路径数量: {path_count}")
    
    return_count = source_content.count("// 返航路径序列")
    print(f"   📊 返航路径数量: {return_count}")

def test_data_integrity():
    """测试数据完整性"""
    print("\n🔍 测试数据完整性")
    print("=" * 50)
    
    test_data = create_test_data()
    
    for i, path_info in enumerate(test_data['path_data'], 1):
        print(f"   📋 路径{i}: 禁飞区{path_info['no_fly_zones']}")
        
        # 检查必需字段
        required_fields = ['no_fly_zones', 'path_sequence', 'path_length', 
                          'return_path', 'return_length']
        
        missing_fields = [field for field in required_fields if field not in path_info]
        
        if not missing_fields:
            print(f"      ✅ 数据完整")
        else:
            print(f"      ❌ 缺少字段: {missing_fields}")
        
        # 检查返航路径长度
        actual_return_length = len(path_info['return_path'])
        declared_return_length = path_info['return_length']
        
        if actual_return_length == declared_return_length:
            print(f"      ✅ 返航路径长度一致: {actual_return_length}")
        else:
            print(f"      ❌ 返航路径长度不一致: 声明{declared_return_length}, 实际{actual_return_length}")
        
        # 检查返航路径范围
        if actual_return_length <= 25:
            print(f"      ✅ 返航路径长度在范围内: {actual_return_length} <= 25")
        else:
            print(f"      ❌ 返航路径长度超出范围: {actual_return_length} > 25")

def test_file_generation():
    """测试文件生成"""
    print("\n💾 测试文件生成")
    print("=" * 50)
    
    # 创建临时目录
    with tempfile.TemporaryDirectory() as temp_dir:
        # 创建测试数据文件
        test_data = create_test_data()
        test_json_file = os.path.join(temp_dir, "test_data.json")
        
        with open(test_json_file, 'w', encoding='utf-8') as f:
            json.dump(test_data, f, indent=2, ensure_ascii=False)
        
        print(f"   📁 测试数据文件: {test_json_file}")
        
        # 修改生成器输出目录
        generator = CCodeGenerator()
        generator.output_dir = temp_dir
        
        try:
            # 加载数据并生成文件
            path_data = generator.load_precomputed_data(test_json_file)
            header_content = generator.generate_header_file(len(path_data))
            source_content = generator.generate_source_file(path_data)
            generator.save_files(header_content, source_content)
            
            # 验证生成的文件
            header_file = os.path.join(temp_dir, "path_storage.h")
            source_file = os.path.join(temp_dir, "path_storage.c")
            
            if os.path.exists(header_file):
                print(f"   ✅ 头文件生成成功: {os.path.getsize(header_file)} 字节")
            else:
                print(f"   ❌ 头文件生成失败")
            
            if os.path.exists(source_file):
                print(f"   ✅ 源文件生成成功: {os.path.getsize(source_file)} 字节")
            else:
                print(f"   ❌ 源文件生成失败")
                
        except Exception as e:
            print(f"   ❌ 文件生成过程中发生错误: {e}")

def main():
    """主测试函数"""
    print("🚀 C代码生成器扩展测试")
    print("版权：米醋电子工作室")
    print("=" * 70)
    
    try:
        # 测试数据结构扩展
        test_data_structure_expansion()
        
        # 测试源文件生成
        test_source_generation()
        
        # 测试数据完整性
        test_data_integrity()
        
        # 测试文件生成
        test_file_generation()
        
        print("\n" + "=" * 70)
        print("🎉 所有测试完成!")
        
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

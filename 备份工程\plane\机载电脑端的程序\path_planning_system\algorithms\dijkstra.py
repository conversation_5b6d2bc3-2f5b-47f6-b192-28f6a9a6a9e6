#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Dijkstra路径规划算法实现
版权信息：米醋电子工作室
创建日期：2025-07-30

功能描述：
Dijkstra算法是一种经典的最短路径算法，保证找到从起点到终点的最优路径。
不使用启发式函数，通过系统性搜索确保路径最优性。

算法特点：
- 保证找到最优路径
- 不需要启发式函数，适用性广
- 搜索过程较为全面，计算量相对较大
- 时间复杂度：O(V²)或O(E + V log V)，空间复杂度：O(V)
"""

import heapq
import numpy as np
import time
from typing import List, Tuple, Dict, Set

# 使用绝对导入
import sys
import os

current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.insert(0, current_dir)
sys.path.insert(0, parent_dir)

try:
    from base_planner import BasePlanner
    from core.grid_map import GridMap
except ImportError:
    # 动态导入
    import importlib.util

    spec = importlib.util.spec_from_file_location("base_planner", os.path.join(current_dir, "base_planner.py"))
    base_module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(base_module)
    BasePlanner = base_module.BasePlanner

    spec = importlib.util.spec_from_file_location("grid_map", os.path.join(parent_dir, "core", "grid_map.py"))
    grid_module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(grid_module)
    GridMap = grid_module.GridMap

class DijkstraPlanner(BasePlanner):
    """
    Dijkstra路径规划算法
    
    使用最短路径搜索找到从起点到终点的最优路径。
    基于NumPy优化，提供可靠的路径搜索能力。
    """
    
    def __init__(self):
        """初始化Dijkstra规划器"""
        super().__init__("Dijkstra算法")
        
        # 性能优化：预分配数据结构
        self._distance_buffer = np.full((GridMap.GRID_ROWS, GridMap.GRID_COLS),
                                       np.inf, dtype=np.float32)
        self._visited_buffer = np.zeros((GridMap.GRID_ROWS, GridMap.GRID_COLS),
                                       dtype=bool)
        self._parent_buffer = np.full((GridMap.GRID_ROWS, GridMap.GRID_COLS, 2), -1, dtype=int)
    
    def _plan_path_impl(self, grid_map: GridMap, start: Tuple[int, int]) -> List[Tuple[int, int]]:
        """
        Dijkstra全点遍历算法核心实现

        参数:
            grid_map: 网格地图
            start: 起始位置 (row, col)

        返回:
            List[Tuple[int, int]]: 遍历所有可访问点的路径序列
        """
        # 收集所有可访问的点
        accessible_points = []
        for row in range(GridMap.GRID_ROWS):
            for col in range(GridMap.GRID_COLS):
                if grid_map.is_valid_position(row, col):
                    accessible_points.append((row, col))

        if not accessible_points:
            return []

        # 使用简单的最近邻策略进行全点遍历
        path = [start]
        visited = {start}
        current_pos = start

        while len(visited) < len(accessible_points):
            # 找到最近的未访问点
            min_distance = float('inf')
            next_pos = None

            for point in accessible_points:
                if point not in visited:
                    distance = self.calculate_euclidean_distance(current_pos, point)
                    if distance < min_distance:
                        min_distance = distance
                        next_pos = point

            if next_pos is None:
                break  # 没有更多可访问的点

            # 添加到路径
            path.append(next_pos)
            visited.add(next_pos)
            current_pos = next_pos

        return path
    
    def _calculate_move_cost(self, from_pos: Tuple[int, int], to_pos: Tuple[int, int]) -> float:
        """
        计算移动代价

        参数:
            from_pos: 起始位置
            to_pos: 目标位置

        返回:
            float: 移动代价
        """
        # 对角线移动代价更高
        dx = abs(to_pos[0] - from_pos[0])
        dy = abs(to_pos[1] - from_pos[1])

        if dx == 1 and dy == 1:
            return 1.414  # sqrt(2) 对角线移动
        else:
            return 1.0    # 直线移动

    def _reconstruct_path(self, start: Tuple[int, int], end: Tuple[int, int]) -> List[int]:
        """
        从parent_buffer重建路径

        参数:
            start: 起始位置
            end: 目标位置

        返回:
            List[int]: position_code序列
        """
        path = []
        current = end

        while current != start:
            # 转换为position_code
            row, col = current
            # 坐标系统：A1B7(左上) A1B1(左下) A9B7(右上) A9B1(右下起点)
            # grid_row=B-1, grid_col=9-A => A=9-grid_col, B=grid_row+1
            row_num = 9 - col  # A部分：1-9
            col_num = row + 1  # B部分：1-7
            position_code = row_num * 10 + col_num
            path.append(position_code)

            # 获取父节点
            parent_row = self._parent_buffer[current[0], current[1], 0]
            parent_col = self._parent_buffer[current[0], current[1], 1]

            if parent_row == -1 or parent_col == -1:
                break  # 路径断裂

            current = (parent_row, parent_col)

        # 添加起点
        if current == start:
            row, col = start
            row_num = 9 - col  # A部分：1-9
            col_num = row + 1  # B部分：1-7
            position_code = row_num * 10 + col_num
            path.append(position_code)

        # 反转路径（从起点到终点）
        path.reverse()
        return path
    
    def find_shortest_paths_to_all(self, grid_map: GridMap, 
                                  start: Tuple[int, int]) -> np.ndarray:
        """
        计算从起点到所有可达点的最短距离
        
        参数:
            grid_map: 网格地图
            start: 起始位置
            
        返回:
            np.ndarray: 距离矩阵
        """
        # 重置缓冲区
        self._distance_buffer.fill(np.inf)
        self._visited_buffer.fill(False)
        
        # 初始化
        priority_queue = []
        heapq.heappush(priority_queue, (0, start))
        self._distance_buffer[start[0], start[1]] = 0
        
        while priority_queue:
            current_distance, current = heapq.heappop(priority_queue)
            
            if self._visited_buffer[current[0], current[1]]:
                continue
            
            self._visited_buffer[current[0], current[1]] = True
            
            # 探索所有邻居
            for neighbor in grid_map.get_neighbors(current[0], current[1]):
                if self._visited_buffer[neighbor[0], neighbor[1]]:
                    continue
                
                move_cost = self._calculate_move_cost(current, neighbor)
                new_distance = current_distance + move_cost
                
                if new_distance < self._distance_buffer[neighbor[0], neighbor[1]]:
                    self._distance_buffer[neighbor[0], neighbor[1]] = new_distance
                    heapq.heappush(priority_queue, (new_distance, neighbor))
        
        return self._distance_buffer.copy()
    
    def _is_optimal(self) -> bool:
        """Dijkstra算法保证最优解"""
        return True
    
    def _get_complexity(self) -> str:
        """获取算法复杂度描述"""
        return "时间复杂度: O(V²) 或 O(E + V log V), 空间复杂度: O(V)"
    
    def get_search_statistics(self) -> dict:
        """
        获取搜索统计信息
        
        返回:
            dict: 搜索统计
        """
        return {
            'algorithm': self.algorithm_name,
            'optimal': True,
            'complete': True,
            'heuristic': '无（系统性搜索）',
            'move_cost': '直线移动=1.0, 对角移动=1.414',
            'suitable_for': ['最优路径保证', '简单环境', '可靠性要求高']
        }
    
    def calculate_return_path(self, grid_map: GridMap,
                             start: Tuple[int, int],
                             end: Tuple[int, int]) -> List[int]:
        """
        计算从起点到终点的返航路径

        参数:
            grid_map: 网格地图
            start: 起始位置（巡查终点）
            end: 目标位置（A9B1起点）

        返回:
            List[int]: 返航路径的position_code序列
        """
        # 使用标准Dijkstra算法计算最短路径
        result = self.plan_path_between_points(grid_map, start, end)

        if result.is_valid():
            return result.path_sequence
        else:
            return []

    def plan_path_between_points(self, grid_map: GridMap,
                                start: Tuple[int, int],
                                end: Tuple[int, int]) -> 'PathResult':
        """
        计算两点之间的最短路径

        参数:
            grid_map: 网格地图
            start: 起始位置
            end: 目标位置

        返回:
            PathResult: 路径规划结果
        """
        from core.path_result import PathResult

        start_time = time.time()

        # 重置缓冲区
        self._distance_buffer.fill(np.inf)
        self._visited_buffer.fill(False)
        self._parent_buffer.fill((-1, -1))

        # 初始化
        priority_queue = []
        heapq.heappush(priority_queue, (0, start))
        self._distance_buffer[start[0], start[1]] = 0

        found_path = False

        while priority_queue:
            current_distance, current = heapq.heappop(priority_queue)

            if self._visited_buffer[current[0], current[1]]:
                continue

            self._visited_buffer[current[0], current[1]] = True

            # 检查是否到达目标
            if current == end:
                found_path = True
                break

            # 探索所有邻居
            for neighbor in grid_map.get_neighbors(current[0], current[1]):
                if self._visited_buffer[neighbor[0], neighbor[1]]:
                    continue

                move_cost = self._calculate_move_cost(current, neighbor)
                new_distance = current_distance + move_cost

                if new_distance < self._distance_buffer[neighbor[0], neighbor[1]]:
                    self._distance_buffer[neighbor[0], neighbor[1]] = new_distance
                    self._parent_buffer[neighbor[0], neighbor[1]] = current
                    heapq.heappush(priority_queue, (new_distance, neighbor))

        # 重建路径
        path_sequence = []
        if found_path:
            path_sequence = self._reconstruct_path(start, end)

        computation_time = (time.time() - start_time) * 1000

        return PathResult(
            path_sequence=path_sequence,
            total_length=self._distance_buffer[end[0], end[1]] if found_path else 0,
            computation_time_ms=computation_time,
            algorithm_name=self.algorithm_name
        )

    def analyze_connectivity(self, grid_map: GridMap,
                           start: Tuple[int, int]) -> Dict[str, any]:
        """
        分析从起点的连通性

        参数:
            grid_map: 网格地图
            start: 起始位置

        返回:
            Dict: 连通性分析结果
        """
        distances = self.find_shortest_paths_to_all(grid_map, start)

        # 统计可达点
        reachable_points = np.sum(np.isfinite(distances))
        total_points = GridMap.GRID_ROWS * GridMap.GRID_COLS

        # 计算平均距离
        finite_distances = distances[np.isfinite(distances)]
        avg_distance = np.mean(finite_distances) if len(finite_distances) > 0 else 0

        return {
            'reachable_points': int(reachable_points),
            'total_points': total_points,
            'connectivity_ratio': reachable_points / total_points,
            'average_distance': float(avg_distance),
            'max_distance': float(np.max(finite_distances)) if len(finite_distances) > 0 else 0,
            'isolated_regions': total_points - reachable_points
        }

# Drv_Uart.c 编码修复计划

## 修复概述
**文件**: `DriversMcu\STM32F4xx\Drivers\Drv_Uart.c`  
**问题**: 大量中文注释显示为"锟斤拷"乱码  
**原因**: GBK/GB2312编码被错误解析为UTF-8  
**修复策略**: 逐行替换乱码为正确的UTF-8中文

## 乱码模式识别

### 1. 常见乱码模式及对应中文
```
锟斤拷锟斤拷锟叫讹拷锟斤拷锟饺硷拷 → 配置中断优先级
锟斤拷锟斤拷IO → 配置IO
锟斤拷锟矫达拷锟斤拷 → 配置串口
锟斤拷锟斤拷UART时锟斤拷 → 使能UART时钟
锟斤拷锟斤拷锟绞匡拷锟斤拷通锟斤拷锟斤拷锟斤拷站锟斤拷锟斤拷 → 波特率设置，通信双方必须一致
8位锟斤拷锟斤拷 → 8位数据
锟斤拷帧锟斤拷尾锟斤拷锟斤拷1锟斤拷停止位 → 一帧数据尾部有1个停止位
锟斤拷锟斤拷锟斤拷偶校锟斤拷 → 无奇偶校验
硬锟斤拷锟斤拷锟斤拷锟斤拷失锟斤拷 → 硬件流控制失能
锟斤拷锟酵★拷锟斤拷锟斤拷使锟斤拷 → 发送和接收使能
锟斤拷锟斤拷时锟斤拷 → 配置时钟
时锟接低碉拷平锟筋动 → 时钟低电平驱动
SLCK锟斤拷锟斤拷锟斤拷时锟斤拷锟斤拷锟斤拷募锟斤拷锟�->锟酵碉拷平 → SLCK时钟输出极性->低电平
时锟接第讹拷锟斤拷锟斤拷锟截斤拷锟斤拷锟斤拷锟捷诧拷锟斤拷 → 时钟第二个边沿进行数据捕获
锟斤拷锟揭晃伙拷锟斤拷莸锟绞憋拷锟斤拷锟斤拷宀伙拷锟絊CLK锟斤拷锟� → 最后一位数据的时钟脉冲不从SCLK输出
使锟斤拷UART锟斤拷锟斤拷锟叫讹拷 → 使能UART接收中断
使锟斤拷UART → 使能UART
锟津开凤拷锟斤拷锟叫讹拷 → 打开发送中断
ORE锟叫讹拷 → ORE中断
锟斤拷锟斤拷锟叫讹拷 → 接收中断
锟斤拷锟斤拷卸媳锟街� → 清除中断标志
锟斤拷锟酵ｏ拷锟斤拷锟斤拷锟斤拷位锟斤拷锟叫讹拷 → 发送，发送缓冲空位中断
写DR锟斤拷锟斤拷卸媳锟街� → 写DR清除中断标志
锟截憋拷TXE锟斤拷锟斤拷锟斤拷锟叫断ｏ拷锟叫讹拷 → 关闭TXE发送缓冲中断
```

## 修复进度

### ✅ 已完成修复
1. **文件头部版权信息** (第2-6行)
2. **串口分配说明** (第21-29行)
3. **TOF注释** (第32行)
4. **SBUS注释** (第35行)
5. **UART1完整修复** (第51-161行)

### 🔄 待修复部分
1. **UART2** (第180-288行) - 25个乱码
2. **UART3** (第304-414行) - 25个乱码
3. **UART4** (第430-531行) - 20个乱码
4. **UART5** (第547-649行) - 20个乱码
5. **UART7** (第665-766行) - 20个乱码
6. **UART8** (第782-883行) - 20个乱码

## 批量修复策略

### 模式1: 初始化函数注释
```c
// 修复前
//锟斤拷锟斤拷锟叫讹拷锟斤拷锟饺硷拷
//锟斤拷锟斤拷IO
//锟斤拷锟矫达拷锟斤拷

// 修复后
//配置中断优先级
//配置IO
//配置串口
```

### 模式2: 参数配置注释
```c
// 修复前
//锟斤拷锟斤拷锟绞匡拷锟斤拷通锟斤拷锟斤拷锟斤拷站锟斤拷锟斤拷
//8位锟斤拷锟斤拷
//锟斤拷帧锟斤拷尾锟斤拷锟斤拷1锟斤拷停止位

// 修复后
//波特率设置，通信双方必须一致
//8位数据
//一帧数据尾部有1个停止位
```

### 模式3: 中断处理注释
```c
// 修复前
//ORE锟叫讹拷
//锟斤拷锟斤拷锟叫讹拷
//锟斤拷锟斤拷卸媳锟街�

// 修复后
//ORE中断
//接收中断
//清除中断标志
```

## 质量保证

### 1. 修复原则
- 只修复注释，不改变代码逻辑
- 保持原有注释的含义和格式
- 确保UTF-8编码正确显示

### 2. 验证方法
- 编译验证：确保修复不影响编译
- 功能验证：确保代码功能不变
- 编码验证：确保UTF-8格式正确

### 3. 风险控制
- 由于Drv_Uart.c遵循既定规范，修改需谨慎
- 每次修复后立即验证
- 保持代码结构完全不变

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试User_Task.c文件清理结果
"""

import re

def test_user_task_cleanup():
    """测试User_Task.c的清理情况"""
    print("🔍 测试User_Task.c冗余代码清理结果")
    print("=" * 70)
    
    # 1. 检查已移除的过时代码
    print("📋 检查已移除的过时代码...")
    
    c_file_path = "../../FcSrc/User_Task.c"
    with open(c_file_path, 'r', encoding='utf-8') as f:
        c_content = f.read()
    
    # 检查过时的函数声明是否已移除
    removed_items = [
        'path_planner_lite_nearest_neighbor',
        '轻量级最近邻算法',
        'lightweight nearest neighbor',
        'Nearest Neighbor',
        'LIGHTWEIGHT PATH PLANNING',
        'Lightweight algorithm failed',
        'Algorithm: Nearest Neighbor'
    ]
    
    removal_success = 0
    for item in removed_items:
        if item not in c_content:
            removal_success += 1
            print(f"✅ 已移除: {item}")
        else:
            print(f"❌ 仍存在: {item}")
    
    print(f"过时代码移除率: {removal_success}/{len(removed_items)} ({removal_success/len(removed_items)*100:.1f}%)")
    
    # 2. 检查保留的核心功能
    print("\n📋 检查保留的核心功能...")
    
    preserved_items = [
        'int get_next_patrol_point(void)',
        'int current_patrol_order = 1',
        'u32 mission_start_time_ms = 0',
        'void setup_default_patrol_path(void)',
        'bool execute_path_planning(void)'
    ]
    
    preservation_success = 0
    for item in preserved_items:
        if item in c_content:
            preservation_success += 1
            print(f"✅ 已保留: {item}")
        else:
            print(f"❌ 缺失: {item}")
    
    print(f"核心功能保留率: {preservation_success}/{len(preserved_items)} ({preservation_success/len(preserved_items)*100:.1f}%)")
    
    # 3. 检查execute_path_planning函数的简化
    print("\n📋 检查execute_path_planning函数简化...")
    
    # 查找execute_path_planning函数
    pattern = r'bool execute_path_planning\(void\)\s*\{(.*?)\}'
    match = re.search(pattern, c_content, re.DOTALL)
    
    if match:
        function_body = match.group(1)
        
        # 检查新的简化逻辑
        simplified_features = [
            'Using precomputed optimal path',
            'Valid path points:',
            'No precomputed path found',
            'work_pos[i][6] == 0 && work_pos[i][5] > 0'
        ]
        
        simplification_success = 0
        for feature in simplified_features:
            if feature in function_body:
                simplification_success += 1
                print(f"✅ 简化功能: {feature}")
            else:
                print(f"❌ 缺少简化功能: {feature}")
        
        print(f"函数简化完成度: {simplification_success}/{len(simplified_features)} ({simplification_success/len(simplified_features)*100:.1f}%)")
        
        # 检查函数长度（应该大幅减少）
        function_lines = len(function_body.strip().split('\n'))
        print(f"函数行数: {function_lines} 行（应该<50行）")
        
        if function_lines < 50:
            print("✅ 函数长度合理")
        else:
            print("❌ 函数仍然过长")
    else:
        print("❌ 未找到execute_path_planning函数")
    
    # 4. 检查状态机的简化
    print("\n📋 检查状态机简化...")
    
    # 查找状态机中的路径规划部分
    if 'execute_path_planning();' in c_content and 'Path planning complete, starting patrol' in c_content:
        print("✅ 状态机已简化")
    else:
        print("❌ 状态机未正确简化")
    
    # 检查是否移除了复杂的错误处理
    if 'Path planning failed, using default path' not in c_content:
        print("✅ 复杂错误处理已移除")
    else:
        print("❌ 复杂错误处理仍存在")
    
    # 5. 检查注释更新
    print("\n📋 检查注释更新...")
    
    updated_comments = [
        '使用预计算路径或默认路径',
        '当没有预计算路径时',
        '按照预计算路径的order顺序'
    ]
    
    comment_update_success = 0
    for comment in updated_comments:
        if comment in c_content:
            comment_update_success += 1
            print(f"✅ 注释已更新: {comment}")
        else:
            print(f"❌ 注释未更新: {comment}")
    
    print(f"注释更新率: {comment_update_success}/{len(updated_comments)} ({comment_update_success/len(updated_comments)*100:.1f}%)")
    
    # 6. 统计代码行数变化
    print("\n📋 统计代码变化...")
    
    total_lines = len(c_content.split('\n'))
    print(f"总行数: {total_lines}")
    
    # 统计函数数量
    function_count = len(re.findall(r'^\w+.*\(.*\)\s*\{', c_content, re.MULTILINE))
    print(f"函数数量: {function_count}")
    
    # 统计调试输出数量
    debug_count = c_content.count('AnoPTv8SendStr')
    print(f"调试输出数量: {debug_count}")
    
    # 7. 兼容性检查
    print("\n📋 检查与预计算路径系统的兼容性...")
    
    compatibility_items = [
        'current_patrol_order',
        'mission_start_time_ms',
        'work_pos[i][5]',  # order字段
        'work_pos[i][6]'   # status字段
    ]
    
    compatibility_success = 0
    for item in compatibility_items:
        if item in c_content:
            compatibility_success += 1
            print(f"✅ 兼容性保持: {item}")
        else:
            print(f"❌ 兼容性问题: {item}")
    
    print(f"兼容性保持率: {compatibility_success}/{len(compatibility_items)} ({compatibility_success/len(compatibility_items)*100:.1f}%)")
    
    # 8. 总结
    print("\n" + "=" * 70)
    print("🎯 User_Task.c清理测试总结:")
    
    overall_score = (
        removal_success / len(removed_items) * 25 +
        preservation_success / len(preserved_items) * 25 +
        simplification_success / len(simplified_features) * 25 +
        compatibility_success / len(compatibility_items) * 25
    )
    
    print(f"总体清理完成度: {overall_score:.1f}%")
    
    if overall_score >= 90:
        print("✅ 清理质量: 优秀")
    elif overall_score >= 80:
        print("✅ 清理质量: 良好")
    elif overall_score >= 70:
        print("⚠️ 清理质量: 一般")
    else:
        print("❌ 清理质量: 需要改进")
    
    print("\n🚀 主要改进:")
    print("   - 移除了所有轻量级路径规划算法相关代码")
    print("   - 简化了execute_path_planning函数逻辑")
    print("   - 保持了与预计算路径系统的完全兼容")
    print("   - 减少了代码复杂度和维护负担")
    print("   - 更新了注释以反映新的架构")
    
    print("\n📋 后续建议:")
    if removal_success < len(removed_items):
        print("   - 继续清理剩余的过时代码和注释")
    if preservation_success < len(preserved_items):
        print("   - 确保所有核心功能都已正确保留")
    if compatibility_success < len(compatibility_items):
        print("   - 检查并修复兼容性问题")
    
    print("   - 进行编译测试确保无错误")
    print("   - 进行功能测试验证路径执行正常")

if __name__ == "__main__":
    test_user_task_cleanup()

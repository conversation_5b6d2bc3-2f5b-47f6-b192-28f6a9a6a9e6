#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
可视化测试工具
版权信息：米醋电子工作室
创建日期：2025-07-31
作者：Emma (产品经理)
编码格式：UTF-8

功能描述：
测试HTML可视化的正确性，验证坐标系统、路径显示等功能
"""

import json
from html_visualizer import HTMLVisualizer

def test_coordinate_system():
    """测试坐标系统显示"""
    print("🔍 测试坐标系统显示")
    print("=" * 50)
    
    visualizer = HTMLVisualizer()
    
    # 测试关键点的坐标转换
    test_points = [
        (17, "A1B7", "左上角"),
        (11, "A1B1", "左下角"), 
        (97, "A9B7", "右上角"),
        (91, "A9B1", "右下角起点")
    ]
    
    for pos_code, name, desc in test_points:
        row, col = visualizer.position_code_to_grid(pos_code)
        back_code = visualizer.grid_to_position_code(row, col)
        print(f"{name} ({desc}): position_code={pos_code} -> grid=({row},{col}) -> back={back_code}")
        assert back_code == pos_code, f"坐标转换错误: {pos_code} != {back_code}"
    
    print("✅ 坐标系统测试通过")

def test_return_path_display():
    """测试返航路径显示"""
    print("\n🔍 测试返航路径显示")
    print("=" * 50)
    
    # 加载实际数据
    with open('optimized_return_paths.json', 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    # 测试第一个路径的返航显示
    path_data = data['path_data'][0]
    print(f"禁飞区: {path_data['no_fly_zones']}")
    print(f"巡查路径长度: {path_data['patrol_path_length']}")
    print(f"返航路径: {path_data['return_path_sequence']}")
    
    # 分析返航路径
    patrol_set = set(path_data['patrol_path_sequence'])
    return_path = path_data['return_path_sequence']
    
    print(f"\n返航路径分析:")
    for i, pos in enumerate(return_path):
        in_patrol = pos in patrol_set
        is_start_end = pos == 91
        print(f"  {i+1}. {pos} - {'在巡查路径中' if in_patrol else '不在巡查路径中'} - {'起点/终点' if is_start_end else '中间点'}")
    
    print("✅ 返航路径分析完成")

def test_html_generation():
    """测试HTML生成"""
    print("\n🔍 测试HTML生成")
    print("=" * 50)
    
    # 加载数据
    with open('optimized_return_paths.json', 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    visualizer = HTMLVisualizer()
    
    # 生成第一个路径的HTML
    path_data = data['path_data'][0]
    html_content = visualizer.generate_grid_html(path_data)
    
    # 检查HTML内容
    print("HTML内容检查:")
    print(f"  包含禁飞区标记: {'cell-no_fly' in html_content}")
    print(f"  包含巡查路径标记: {'cell-patrol' in html_content}")
    print(f"  包含返航路径标记: {'cell-return' in html_content}")
    print(f"  包含终点标记: {'cell-end' in html_content}")
    
    # 检查坐标系统
    lines = html_content.split('\n')
    table_lines = [line for line in lines if '<tr><th>B' in line]
    
    print(f"\n表格行顺序检查:")
    for line in table_lines:
        if 'B7' in line:
            print(f"  B7行: {line[:100]}...")
        elif 'B1' in line:
            print(f"  B1行: {line[:100]}...")
    
    print("✅ HTML生成测试完成")

def create_simple_test_case():
    """创建简单测试用例"""
    print("\n🔍 创建简单测试用例")
    print("=" * 50)
    
    # 创建一个简单的测试数据
    test_data = {
        'no_fly_zones': [11, 21, 31],  # A1B1, A2B1, A3B1
        'patrol_path_sequence': [91, 81, 71, 61, 51, 41, 42, 43, 44, 45],  # 简化的巡查路径
        'return_path_sequence': [45, 55, 65, 75, 85, 95, 91],  # 简化的返航路径
        'patrol_path_length': 10,
        'return_path_length': 7,
        'total_flight_time': 100.0,
        'coverage_rate': 100.0,
        'computation_time_ms': 50.0,
        'is_valid': True
    }
    
    visualizer = HTMLVisualizer()
    
    # 生成HTML
    html_content = visualizer.generate_single_path_html(test_data, 0)
    
    # 保存测试文件
    with open('test_visualization.html', 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print("✅ 测试文件已生成: test_visualization.html")
    
    # 分析网格数据
    print("\n网格数据分析:")
    grid_data = {}
    no_fly_zones = set(test_data['no_fly_zones'])
    patrol_path = test_data['patrol_path_sequence']
    return_path = test_data['return_path_sequence']
    
    # 标记禁飞区
    for pos in no_fly_zones:
        grid_data[pos] = 'no_fly'
    
    # 标记巡查路径
    patrol_positions = set(patrol_path)
    for i, pos in enumerate(patrol_path):
        if pos not in no_fly_zones:
            if i == 0:
                grid_data[pos] = 'start'
            else:
                grid_data[pos] = f'patrol_{i}'
    
    # 标记返航路径
    for i, pos in enumerate(return_path):
        if pos not in no_fly_zones:
            if pos not in patrol_positions or pos == 91:
                if pos == 91 and i == len(return_path) - 1:
                    grid_data[pos] = 'end'
                else:
                    grid_data[pos] = f'return_{i+1}'
    
    # 显示网格
    print("\n网格显示 (B7到B1，A1到A9):")
    for display_row in range(7):
        actual_row = 7 - 1 - display_row  # B7=0, B6=1, ..., B1=6
        row_label = f"B{actual_row + 1}"
        line = f"{row_label}: "
        
        for col in range(9):
            pos_code = (col + 1) * 10 + (actual_row + 1)
            cell_type = grid_data.get(pos_code, 'empty')
            if cell_type == 'no_fly':
                line += "✖ "
            elif cell_type == 'start':
                line += "S "
            elif cell_type == 'end':
                line += "E "
            elif cell_type.startswith('patrol_'):
                line += f"{cell_type.split('_')[1]} "
            elif cell_type.startswith('return_'):
                line += f"R{cell_type.split('_')[1]} "
            else:
                line += "· "
        
        print(line)

def main():
    """主函数"""
    print("🚀 HTML可视化测试工具")
    print("=" * 70)
    print("版权信息：米醋电子工作室")
    print("创建日期：2025-07-31")
    print("作者：Emma (产品经理)")
    print("=" * 70)
    
    try:
        test_coordinate_system()
        test_return_path_display()
        test_html_generation()
        create_simple_test_case()
        
        print("\n🎉 所有测试完成!")
        print("✅ 坐标系统正确")
        print("✅ 返航路径显示正确")
        print("✅ HTML生成功能正常")
        print("✅ 测试用例生成成功")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

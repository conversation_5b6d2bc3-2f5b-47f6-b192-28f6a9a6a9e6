
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>无人机路径规划可视化 - 组合 1</title>
            
        <style>
            body {
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                margin: 20px;
                background-color: #f5f5f5;
            }
            
            .container {
                max-width: 1200px;
                margin: 0 auto;
                background-color: white;
                padding: 20px;
                border-radius: 10px;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            }
            
            h1 {
                text-align: center;
                color: #2c3e50;
                margin-bottom: 30px;
                font-size: 28px;
            }
            
            h2 {
                color: #34495e;
                border-bottom: 2px solid #3498db;
                padding-bottom: 10px;
                margin-top: 30px;
            }
            
            .grid-container {
                margin: 20px 0;
                overflow-x: auto;
            }
            
            .path-grid {
                border-collapse: collapse;
                margin: 20px auto;
                font-family: 'Courier New', monospace;
                font-size: 12px;
                background-color: white;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            }
            
            .path-grid th, .path-grid td {
                width: 40px;
                height: 40px;
                text-align: center;
                vertical-align: middle;
                border: 1px solid #bdc3c7;
                position: relative;
                font-weight: bold;
            }
            
            .path-grid th {
                background-color: #ecf0f1;
                color: #2c3e50;
                font-size: 11px;
            }
            
            /* 网格单元格样式 */
            .cell-empty {
                background-color: #ffffff;
                color: #95a5a6;
            }
            
            .cell-no-fly {
                background-color: #e74c3c;
                color: white;
                font-weight: bold;
            }
            
            .cell-start {
                background-color: #f39c12;
                color: white;
                font-weight: bold;
                font-size: 14px;
            }
            
            .cell-patrol {
                background-color: #3498db;
                color: white;
                font-weight: bold;
                font-size: 10px;
            }
            
            .cell-return {
                background-color: #27ae60;
                color: white;
                font-weight: bold;
                font-size: 10px;
            }
            
            .cell-end {
                background-color: #9b59b6;
                color: white;
                font-weight: bold;
                font-size: 14px;
            }
            
            /* 箭头样式 */
            .arrow {
                position: absolute;
                font-size: 16px;
                font-weight: bold;
            }
            
            .arrow-right { right: 2px; top: 50%; transform: translateY(-50%); }
            .arrow-left { left: 2px; top: 50%; transform: translateY(-50%); }
            .arrow-down { bottom: 2px; left: 50%; transform: translateX(-50%); }
            .arrow-up { top: 2px; left: 50%; transform: translateX(-50%); }
            .arrow-dr { bottom: 2px; right: 2px; }
            .arrow-dl { bottom: 2px; left: 2px; }
            .arrow-ur { top: 2px; right: 2px; }
            .arrow-ul { top: 2px; left: 2px; }
            
            /* 图例样式 */
            .legend {
                display: flex;
                flex-wrap: wrap;
                justify-content: center;
                gap: 20px;
                margin: 20px 0;
                padding: 15px;
                background-color: #f8f9fa;
                border-radius: 8px;
            }
            
            .legend-item {
                display: flex;
                align-items: center;
                gap: 8px;
            }
            
            .legend-color {
                width: 20px;
                height: 20px;
                border-radius: 3px;
                border: 1px solid #bdc3c7;
            }
            
            /* 统计信息样式 */
            .stats {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 15px;
                margin: 20px 0;
            }
            
            .stat-item {
                background-color: #f8f9fa;
                padding: 15px;
                border-radius: 8px;
                text-align: center;
                border-left: 4px solid #3498db;
            }
            
            .stat-value {
                font-size: 24px;
                font-weight: bold;
                color: #2c3e50;
            }
            
            .stat-label {
                font-size: 14px;
                color: #7f8c8d;
                margin-top: 5px;
            }
            
            /* 响应式设计 */
            @media (max-width: 768px) {
                .path-grid th, .path-grid td {
                    width: 30px;
                    height: 30px;
                    font-size: 10px;
                }
                
                .legend {
                    flex-direction: column;
                    align-items: center;
                }
            }
        </style>
        
        </head>
        <body>
            <div class="container">
                <h1>无人机路径规划可视化</h1>
                <h2>禁飞区组合 1: [11, 21, 31]</h2>
                
                
        <div class="legend">
            <div class="legend-item">
                <div class="legend-color" style="background-color: #f39c12;"></div>
                <span>起点 (A9B1)</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #e74c3c;"></div>
                <span>禁飞区</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #3498db;"></div>
                <span>巡查路径</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #27ae60;"></div>
                <span>返航路径</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #9b59b6;"></div>
                <span>终点 (返回起点)</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #ffffff; border: 1px solid #bdc3c7;"></div>
                <span>未访问区域</span>
            </div>
        </div>
        
                
                <div class="grid-container">
<table class="path-grid">
<tr><th></th><th>A1</th><th>A2</th><th>A3</th><th>A4</th><th>A5</th><th>A6</th><th>A7</th><th>A8</th><th>A9</th><th></th></tr>
<tr><th>B7</th><td class="cell-empty">·</td><td class="cell-empty">·</td><td class="cell-empty">·</td><td class="cell-empty">·</td><td class="cell-empty">·</td><td class="cell-empty">·</td><td class="cell-empty">·</td><td class="cell-empty">·</td><td class="cell-empty">·</td><th>B7</th></tr>
<tr><th>B6</th><td class="cell-empty">·</td><td class="cell-empty">·</td><td class="cell-empty">·</td><td class="cell-empty">·</td><td class="cell-empty">·</td><td class="cell-empty">·</td><td class="cell-empty">·</td><td class="cell-empty">·</td><td class="cell-empty">·</td><th>B6</th></tr>
<tr><th>B5</th><td class="cell-empty">·</td><td class="cell-empty">·</td><td class="cell-empty">·</td><td class="cell-patrol">9</td><td class="cell-return">R2</td><td class="cell-return">R3</td><td class="cell-return">R4</td><td class="cell-return">R5</td><td class="cell-return">R6</td><th>B5</th></tr>
<tr><th>B4</th><td class="cell-empty">·</td><td class="cell-empty">·</td><td class="cell-empty">·</td><td class="cell-patrol">8</td><td class="cell-empty">·</td><td class="cell-empty">·</td><td class="cell-empty">·</td><td class="cell-empty">·</td><td class="cell-empty">·</td><th>B4</th></tr>
<tr><th>B3</th><td class="cell-empty">·</td><td class="cell-empty">·</td><td class="cell-empty">·</td><td class="cell-patrol">7</td><td class="cell-empty">·</td><td class="cell-empty">·</td><td class="cell-empty">·</td><td class="cell-empty">·</td><td class="cell-empty">·</td><th>B3</th></tr>
<tr><th>B2</th><td class="cell-empty">·</td><td class="cell-empty">·</td><td class="cell-empty">·</td><td class="cell-patrol">6</td><td class="cell-empty">·</td><td class="cell-empty">·</td><td class="cell-empty">·</td><td class="cell-empty">·</td><td class="cell-empty">·</td><th>B2</th></tr>
<tr><th>B1</th><td class="cell-no_fly">✖</td><td class="cell-no_fly">✖</td><td class="cell-no_fly">✖</td><td class="cell-patrol">5</td><td class="cell-patrol">4</td><td class="cell-patrol">3</td><td class="cell-patrol">2</td><td class="cell-patrol">1</td><td class="cell-end">E</td><th>B1</th></tr>
<tr><th></th><th>A1</th><th>A2</th><th>A3</th><th>A4</th><th>A5</th><th>A6</th><th>A7</th><th>A8</th><th>A9</th><th></th></tr>
</table>
</div>

                
                <div class="stats">

            <div class="stat-item">
                <div class="stat-value">3</div>
                <div class="stat-label">禁飞区数量</div>
            </div>
            
            <div class="stat-item">
                <div class="stat-value">10</div>
                <div class="stat-label">巡查路径长度</div>
            </div>
            
            <div class="stat-item">
                <div class="stat-value">7</div>
                <div class="stat-label">返航路径长度</div>
            </div>
            
            <div class="stat-item">
                <div class="stat-value">100.0秒</div>
                <div class="stat-label">总飞行时间</div>
            </div>
            
            <div class="stat-item">
                <div class="stat-value">100.0%</div>
                <div class="stat-label">覆盖率</div>
            </div>
            
            <div class="stat-item">
                <div class="stat-value">50.00ms</div>
                <div class="stat-label">计算时间</div>
            </div>
            </div>

                
                <h2>路径详情</h2>
                <div style="background-color: #f8f9fa; padding: 15px; border-radius: 8px; margin: 20px 0;">
                    <p><strong>巡查路径序列:</strong></p>
                    <p style="font-family: monospace; word-break: break-all; line-height: 1.6;">
                        91 → 81 → 71 → 61 → 51 → 41 → 42 → 43 → 44 → 45
                        
                    </p>
                    
                    <p><strong>返航路径序列:</strong></p>
                    <p style="font-family: monospace; word-break: break-all; line-height: 1.6;">
                        45 → 55 → 65 → 75 → 85 → 95 → 91
                    </p>
                </div>
                
                <div style="text-align: center; margin-top: 30px; color: #7f8c8d; font-size: 12px;">
                    <p>版权信息：米醋电子工作室 | 生成时间：2025-07-31 18:24:44</p>
                </div>
            </div>
        </body>
        </html>
        
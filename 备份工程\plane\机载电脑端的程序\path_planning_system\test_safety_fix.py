#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试安全约束修正
版权信息：米醋电子工作室
创建日期：2025-07-31
作者：Alex (工程师)
编码格式：UTF-8

功能描述：
测试修正后的安全约束是否能正确阻止不安全的对角线移动
"""

from core.grid_map import GridMap

def test_diagonal_safety():
    """测试对角线移动安全性"""
    print("🔍 测试对角线移动安全约束")
    print("=" * 50)
    
    # 创建网格地图
    grid_map = GridMap()
    
    # 设置禁飞区 [12, 13, 14] (第50个组合)
    no_fly_zones = [12, 13, 14]
    grid_map.set_no_fly_zones(no_fly_zones)
    
    print(f"禁飞区设置: {no_fly_zones}")
    
    # 测试从11到22的移动
    # 11 = A1B1 -> 网格坐标 (0, 0)
    # 22 = A2B2 -> 网格坐标 (1, 1)
    from_pos = grid_map.position_code_to_grid(11)  # (0, 0)
    to_pos = grid_map.position_code_to_grid(22)    # (1, 1)
    
    print(f"\n测试移动: 11({from_pos}) → 22({to_pos})")
    
    # 获取从11位置的邻居
    neighbors = grid_map.get_neighbors(from_pos[0], from_pos[1])
    
    # 检查22是否在邻居列表中
    is_22_accessible = to_pos in neighbors
    
    print(f"22是否可达: {'❌ 不可达' if not is_22_accessible else '✅ 可达'}")
    
    if not is_22_accessible:
        print("✅ 安全约束生效：阻止了不安全的对角线移动")
    else:
        print("❌ 安全约束失效：仍然允许不安全的移动")
    
    # 显示所有可达的邻居
    print(f"\n从11可达的邻居:")
    for neighbor in neighbors:
        pos_code = grid_map.grid_to_position_code(neighbor[0], neighbor[1])
        print(f"  {pos_code} {neighbor}")
    
    # 测试其他对角线移动
    print(f"\n测试其他对角线移动:")
    
    test_cases = [
        (11, 22, "经过禁飞区12"),
        (21, 32, "经过禁飞区22"),
        (31, 42, "不经过禁飞区"),
        (41, 52, "不经过禁飞区")
    ]
    
    for from_code, to_code, description in test_cases:
        from_grid = grid_map.position_code_to_grid(from_code)
        to_grid = grid_map.position_code_to_grid(to_code)
        
        if from_grid == (-1, -1) or to_grid == (-1, -1):
            continue
            
        neighbors = grid_map.get_neighbors(from_grid[0], from_grid[1])
        is_accessible = to_grid in neighbors
        
        status = "✅ 可达" if is_accessible else "❌ 不可达"
        print(f"  {from_code} → {to_code} ({description}): {status}")

def test_safety_method():
    """测试安全检查方法"""
    print(f"\n🔍 测试安全检查方法")
    print("=" * 50)
    
    grid_map = GridMap()
    grid_map.set_no_fly_zones([12, 13, 14])
    
    # 测试_is_diagonal_move_safe方法
    test_moves = [
        ((0, 0), (1, 1), "11→22，经过12"),  # 11→22
        ((1, 0), (2, 1), "21→32，经过22"),  # 21→32  
        ((2, 0), (3, 1), "31→42，不经过禁飞区"),  # 31→42
        ((0, 1), (1, 2), "12→23，起点是禁飞区"),  # 12→23
    ]
    
    for from_pos, to_pos, description in test_moves:
        is_safe = grid_map._is_diagonal_move_safe(from_pos[0], from_pos[1], 
                                                 to_pos[0], to_pos[1])
        status = "✅ 安全" if is_safe else "❌ 不安全"
        print(f"  {description}: {status}")

def main():
    """主函数"""
    print("🚀 安全约束修正测试")
    print("=" * 70)
    print("版权信息：米醋电子工作室")
    print("创建日期：2025-07-31")
    print("作者：Alex (工程师)")
    print("=" * 70)
    
    test_diagonal_safety()
    test_safety_method()
    
    print(f"\n🎉 安全约束测试完成!")
    print("如果修正成功，11→22的移动应该被阻止")

if __name__ == "__main__":
    main()

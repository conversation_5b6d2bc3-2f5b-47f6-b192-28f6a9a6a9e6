#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Dijkstra算法起点
"""

from algorithms.dijkstra import <PERSON><PERSON><PERSON>Planner
from core.grid_map import GridMap

def test_dijkstra_start_point():
    """测试Dijkstra算法的起点"""
    print("🔍 测试Dijkstra算法起点")
    print("=" * 50)
    
    # 创建网格地图
    grid_map = GridMap()
    
    # 设置简单的禁飞区
    no_fly_zones = [33, 34, 35]  # A3B3, A3B4, A3B5
    grid_map.set_no_fly_zones(no_fly_zones)
    
    # 创建Dijkstra规划器
    planner = DijkstraPlanner()
    
    # 测试起点 A9B1 = (0, 8)
    start_pos = (0, 8)
    print(f"起点网格坐标: {start_pos}")
    print(f"起点position_code: {grid_map.grid_to_position_code(start_pos[0], start_pos[1])}")
    
    # 执行路径规划
    result = planner.plan_path(grid_map, start_pos)
    
    print(f"路径规划成功: {'✅' if result.is_valid() else '❌'}")
    
    if result.is_valid():
        path_sequence = result.path_sequence
        print(f"路径长度: {len(path_sequence)} 点")
        print(f"起点: {path_sequence[0] if path_sequence else 'None'}")
        print(f"终点: {path_sequence[-1] if path_sequence else 'None'}")
        print(f"前10个点: {path_sequence[:10]}")
        
        # 验证起点是否正确
        expected_start = 91  # A9B1
        actual_start = path_sequence[0] if path_sequence else None
        print(f"起点正确: {'✅' if actual_start == expected_start else '❌'}")
        print(f"预期起点: {expected_start}, 实际起点: {actual_start}")
    else:
        print("路径规划失败")

if __name__ == "__main__":
    test_dijkstra_start_point()

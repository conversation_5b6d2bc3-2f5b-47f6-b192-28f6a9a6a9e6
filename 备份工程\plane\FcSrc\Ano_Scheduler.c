/******************** (C) COPYRIGHT 2017 ANO Tech ********************************
 * ��Ȩ�������ƣ������Ƽ���Դ�ɿش����
 * ������www��otc.com
 * �Ա�  ��a��c.taobao.com
 * ����190��595
 * ��Ȩ�������ƣ������Ƽ���Դ�ɿش����
**********************************************************************************/
#include "Ano_Scheduler.h"
#include "User_Task.h"
#include "Drv_led.h"
#include "Drv_Adc.h"
#include "AnoPTv8.h"
#include "Tofsense-m.h"
#include "PID.h"
#include "zigbee.h"  // 包含雷达坐标发送函数声明



//////////////////////////////////////////////////////////////////////
//�û����������
//////////////////////////////////////////////////////////////////////



static void Loop_1000Hz(void) //1msִ�д�
{
    //////////////////////////////////////////////////////////////////////

    //////////////////////////////////////////////////////////////////////
}

static void Loop_500Hz(void) //2msִ�д�
{
    //////////////////////////////////////////////////////////////////////

    //////////////////////////////////////////////////////////////////////
}

static void Loop_200Hz(void) //5msִ�д�
{
    //////////////////////////////////////////////////////////////////////

    //////////////////////////////////////////////////////////////////////
}

static void Loop_100Hz(void) //10msִ�д�
{
    //////////////////////////////////////////////////////////////////////
      //tof_update();
	OutLoop_Control_Z();
	OutLoop_Control_XY();
	OutLoop_Control_yaw();

	// LED PWM���� - ��ά����λ��LEDָʾ
	LED_PWM_Control();
    //////////////////////////////////////////////////////////////////////
}

static void Loop_50Hz(void) //20msִ�д�
{
    //////////////////////////////////////////////////////////////////////
    UserTask_OneKeyCmd();


    //////////////////////////////////////////////////////////////////////
}

static void Loop_20Hz(void) //50msִ�д�
{
    //////////////////////////////////////////////////////////////////////
       if(!USE_PMU)
       {
              DrvAdcCal();
              AnoPTv8SendFrame0x0D(LT_D_IMU, ANOPTV8DEVID_LXIMU, AdcVal_Bat, 0, 0, 0);
       }
    //////////////////////////////////////////////////////////////////////
}

static void Loop_2Hz(void) //500msִ�д�
{
	    // 雷达坐标发送控制（50Hz频率）
    zigbee_send_radar_coordinates();  // 发送雷达坐标数据
       static uint8_t _ledflag = 0;
       _ledflag++;
       if(_ledflag%2)
              DvrLedObOff;
       else
              DvrLedObOn;
}
//////////////////////////////////////////////////////////////////////
//����������
//////////////////////////////////////////////////////////////////////
//ϵͳ�������ã���������������
static sched_task_t sched_tasks[] =
{
    {Loop_1000Hz, 1000, 0, 0},
    {Loop_500Hz, 500, 0, 0},
    {Loop_200Hz, 200, 0, 0},
    {Loop_100Hz, 100, 0, 0},
    {Loop_50Hz, 50, 0, 0},
    {Loop_20Hz, 20, 0, 0},
    {Loop_2Hz, 2, 0, 0},
};
//�����������ȣ��ж��߳�����
#define TASK_NUM (sizeof(sched_tasks) / sizeof(sched_task_t))

void Scheduler_Setup(void)
{
    uint8_t index = 0;
    //���λ������
    for (index = 0; index < TASK_NUM; index++)
    {
        //����ĸ��������ʱ������
        sched_tasks[index].interval_ticks = TICK_PER_SECOND / sched_tasks[index].rate_hz;
        //�������Ϊs������
        if (sched_tasks[index].interval_ticks < 1)
        {
            sched_tasks[index].interval_ticks = 1;
        }
    }
}
//��������ŵ�mile�������Ƿ����߳�Ӧ��ִ�У����ж��Ƿ����̸߳���
void Scheduler_Run(void)
{
    uint8_t index = 0;
    //ѭ���ж������ߣ��Ƿ����

    for (index = 0; index < TASK_NUM; index++)
    {
        //��ͳ��ǰ����λ�䵥
        uint32_t tnow = GetSysRunTimeMs();
        //��ִ�Ƿ��������еļ�
        if (tnow - sched_tasks[index].last_run >= sched_tasks[index].interval_ticks)
        {

            //���������ϴ��еļ�
            sched_tasks[index].last_run = tnow;
            //ִ��������
            sched_tasks[index].task_func();
        }
    }
}

/******************* (C) COPYRIGHT 2014 ANO TECH *****END OF FILE************/



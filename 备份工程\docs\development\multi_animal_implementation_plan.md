# 多动物识别启用方案设计（优化版）

## 1. 方案概述

### 1.1 设计目标
- 启用已实现的多动物识别算法框架
- 替换当前User_Task.c第1169-1194行的单动物识别逻辑
- 实现一个方格内多种动物的稳定识别和循环发送
- 保持现有架构的完全兼容性
- **新增**: 优化识别速度，适应竞赛时间要求

### 1.2 核心原则
- **兼容性优先**: 不影响其他模块功能
- **时间效率**: 快速响应，不死等固定采样次数
- **两阶段识别**: 发现阶段+确认阶段，提高识别效率
- **状态管理**: 确保采样和发送状态的正确管理
- **错误处理**: 完善的异常情况处理机制

### 1.3 算法优化策略
- **发现阶段**: 前期快速发现所有可能的动物ID+count组合
- **确认阶段**: 对发现的候选动物进行稳定性确认
- **动态阈值**: 根据实际情况调整确认阈值
- **早期退出**: 满足条件时立即输出结果

## 2. 优化后的两阶段识别算法

### 2.1 新的数据结构设计

#### 2.1.1 候选动物结构
```c
// 候选动物结构
typedef struct {
    u8 animal_id;        // 动物ID (1-5)
    u8 count;            // 动物数量
    u8 detection_times;  // 检测到的次数
    bool is_candidate;   // 是否为候选动物
} candidate_animal_t;

// 识别阶段枚举
typedef enum {
    DISCOVERY_PHASE,     // 发现阶段
    CONFIRMATION_PHASE   // 确认阶段
} recognition_phase_t;
```

#### 2.1.2 状态管理变量
```c
static recognition_phase_t current_phase = DISCOVERY_PHASE;
static candidate_animal_t candidates[5];           // 候选动物列表
static u8 candidate_count = 0;                     // 候选动物数量
static u8 total_detections = 0;                    // 总检测次数
static u32 phase_start_time = 0;                   // 阶段开始时间
```

### 2.2 发现阶段算法

#### 2.2.1 候选动物发现
```c
// 发现阶段：寻找所有可能的动物ID+count组合
bool add_candidate_animal(u8 animal_id, u8 count) {
    // 检查是否已存在相同的ID+count组合
    for (u8 i = 0; i < candidate_count; i++) {
        if (candidates[i].animal_id == animal_id && candidates[i].count == count) {
            candidates[i].detection_times++;
            return true;  // 已存在，增加检测次数
        }
    }

    // 添加新的候选动物
    if (candidate_count < 5) {
        candidates[candidate_count].animal_id = animal_id;
        candidates[candidate_count].count = count;
        candidates[candidate_count].detection_times = 1;
        candidates[candidate_count].is_candidate = true;
        candidate_count++;
        return true;
    }

    return false;  // 候选列表已满
}
```

#### 2.2.2 阶段转换条件
```c
// 检查是否应该进入确认阶段
bool should_enter_confirmation_phase(void) {
    // 条件1：发现了足够多的不同动物组合
    if (candidate_count >= 3) return true;

    // 条件2：达到最大发现时间（3秒）
    if (GetSysRunTimeMs() - phase_start_time > 3000) return true;

    // 条件3：总检测次数达到上限（15次）
    if (total_detections >= 15) return true;

    // 条件4：某个候选动物已经达到稳定阈值
    for (u8 i = 0; i < candidate_count; i++) {
        if (candidates[i].detection_times >= 5) return true;
    }

    return false;
}
```

### 2.2 数据采集机制

#### 2.2.1 采样数据收集
```c
// 在采样状态下收集数据
if (animal_sampling_active && animal_sample_count < 6) {
    // 存储当前识别结果
    animal_sample_buffer[animal_sample_count].animal_id = maixcam.id;
    animal_sample_buffer[animal_sample_count].count = maixcam.count;
    animal_sample_count++;
    
    // 调试输出
    char debug_info[64];
    sprintf(debug_info, "Sample %d: ID=%d, Count=%d", 
            animal_sample_count, maixcam.id, maixcam.count);
    AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_BLUE, debug_info);
}
```

#### 2.2.2 采样完成检测
```c
// 检查采样是否完成
if (animal_sampling_active && animal_sample_count >= 6) {
    // 采样完成，触发融合算法
    u8 stable_count = process_multi_animal_fusion(animal_sample_buffer, 6, &current_fusion_result);
    
    // 重置采样状态
    animal_sampling_active = false;
    animal_sample_count = 0;
    current_send_index = 0;
    
    // 调试输出
    char fusion_info[64];
    sprintf(fusion_info, "Fusion complete: %d stable animals detected", stable_count);
    AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_GREEN, fusion_info);
}
```

### 2.3 融合算法调用

#### 2.3.1 调用时机
- 采样计数达到6次时自动触发
- 调用现有的process_multi_animal_fusion函数
- 结果存储在current_fusion_result中

#### 2.3.2 结果处理
```c
// 融合算法调用
u8 stable_count = process_multi_animal_fusion(animal_sample_buffer, 6, &current_fusion_result);

if (stable_count > 0) {
    // 有稳定识别结果，准备循环发送
    current_send_index = 0;
    
    // 记录融合结果
    for (u8 i = 0; i < stable_count; i++) {
        char result_info[64];
        sprintf(result_info, "Stable animal %d: ID=%d, Count=%d", 
                i+1, current_fusion_result.animals[i].animal_id, 
                current_fusion_result.animals[i].count);
        AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_CYAN, result_info);
    }
} else {
    // 无稳定识别结果
    AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_YELLOW, 
                  "No stable animals detected");
}
```

### 2.4 循环发送控制

#### 2.4.1 发送逻辑
```c
// 循环发送稳定识别的动物
if (current_fusion_result.animal_count > 0 && current_send_index < current_fusion_result.animal_count) {
    stable_animal_t* current_animal = &current_fusion_result.animals[current_send_index];
    
    // 检查当前位置是否已发送过动物数据
    if (!is_position_code_sent(current_position_code)) {
        // 发送当前动物数据
        zigbee_send_screen_animal(current_position_code, 
                                 current_animal->animal_id, 
                                 current_animal->count);
        
        // 标记位置已发送
        mark_position_code_sent(current_position_code);
        
        // 调试输出
        char send_info[64];
        sprintf(send_info, "Sent animal %d/%d: ID=%d, Count=%d", 
                current_send_index + 1, current_fusion_result.animal_count,
                current_animal->animal_id, current_animal->count);
        AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_GREEN, send_info);
        
        // 移动到下一个动物
        current_send_index++;
    } else {
        // 位置已发送，跳过所有动物
        char skip_info[64];
        sprintf(skip_info, "Position %d already sent, skipping all animals", 
                current_position_code);
        AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_RED, skip_info);
        
        // 重置发送索引
        current_send_index = current_fusion_result.animal_count;
    }
}
```

#### 2.4.2 发送完成处理
```c
// 检查是否所有动物都已发送
if (current_send_index >= current_fusion_result.animal_count) {
    // 所有动物发送完成，重置状态
    current_fusion_result.animal_count = 0;
    current_send_index = 0;
    
    AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_GREEN, 
                  "All animals sent, ready for next detection");
}
```

### 2.5 状态重置机制

#### 2.5.1 方格切换重置
```c
// 在方格切换时重置所有采样状态
static u8 last_position_code = 0;

if (current_position_code != last_position_code) {
    // 方格切换，重置采样状态
    animal_sampling_active = false;
    animal_sample_count = 0;
    current_fusion_result.animal_count = 0;
    current_send_index = 0;
    
    // 更新位置记录
    last_position_code = current_position_code;
    
    // 调试输出
    char reset_info[64];
    sprintf(reset_info, "Position changed to %d, sampling state reset", 
            current_position_code);
    AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_YELLOW, reset_info);
}
```

#### 2.5.2 任务重启重置
```c
// 在任务重启时重置所有状态
void reset_multi_animal_state(void) {
    animal_sampling_active = false;
    animal_sample_count = 0;
    current_fusion_result.animal_count = 0;
    current_send_index = 0;
    memset(animal_sample_buffer, 0, sizeof(animal_sample_buffer));
    
    AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_CYAN, 
                  "Multi-animal state reset");
}
```

### 2.6 保持现有功能

#### 2.6.1 重复发送防护
- **保持机制**: 继续使用is_position_code_sent和mark_position_code_sent
- **应用范围**: 对整个position_code进行防护，而非单个动物
- **逻辑调整**: 一旦位置发送过，跳过该位置的所有动物

#### 2.6.2 激光笔控制
```c
// 保持激光笔控制逻辑
if (current_fusion_result.animal_count > 0 && current_send_index < current_fusion_result.animal_count) {
    // 使用当前发送动物的位置信息控制激光笔
    if (maixcam.x < 30) {
        jiguang(1, 0, 0);  // 红色
    } else if (30 < maixcam.x && maixcam.x < 110) {
        jiguang(0, 1, 0);  // 绿色
    } else {
        jiguang(0, 0, 1);  // 蓝色
    }
} else {
    // 默认绿色激光
    jiguang(0, 1, 0);
}
```

## 3. 实现流程图

```
开始
  ↓
检测到动物? → 否 → 默认激光控制 → 结束
  ↓ 是
采样状态激活? → 否 → 启动采样状态
  ↓ 是
收集采样数据
  ↓
采样完成(6次)? → 否 → 继续采样
  ↓ 是
调用融合算法
  ↓
有稳定结果? → 否 → 重置状态 → 结束
  ↓ 是
循环发送动物数据
  ↓
位置已发送? → 是 → 跳过发送
  ↓ 否
发送当前动物
  ↓
所有动物发送完? → 否 → 下一个动物
  ↓ 是
激光笔控制
  ↓
重置状态
  ↓
结束
```

## 4. 兼容性保证

### 4.1 架构兼容性
- **模块边界**: 所有修改限制在User_Task.c内部
- **接口保持**: 不修改对外接口和函数签名
- **数据结构**: 使用现有的数据结构，无新增依赖

### 4.2 功能兼容性
- **通信协议**: 继续使用zigbee_send_screen_animal函数
- **防护机制**: 保持position_code重复发送防护
- **控制逻辑**: 保持激光笔控制功能

### 4.3 性能兼容性
- **内存使用**: 使用现有的静态变量，无额外内存分配
- **执行效率**: 算法复杂度可控，不影响实时性
- **资源占用**: 总体资源占用增加很少

## 5. 风险评估与缓解

### 5.1 技术风险
- **风险**: 状态管理复杂可能导致状态混乱
- **缓解**: 添加完善的状态重置机制和调试输出

### 5.2 兼容性风险
- **风险**: 修改可能影响其他功能
- **缓解**: 严格限制修改范围，保持现有接口不变

### 5.3 性能风险
- **风险**: 多动物处理可能影响实时性
- **缓解**: 算法优化，使用高效的数据结构

## 6. 测试策略

### 6.1 单元测试
- 测试采样状态管理
- 测试融合算法调用
- 测试循环发送逻辑

### 6.2 集成测试
- 测试与现有功能的兼容性
- 测试多种动物识别场景
- 测试异常情况处理

### 6.3 系统测试
- 完整的多动物识别流程测试
- 性能和稳定性测试
- 长时间运行测试

## 7. 实施计划

### 7.1 第一阶段: 采样状态管理
- 实现采样启动和数据收集
- 添加状态重置机制
- 基础调试输出

### 7.2 第二阶段: 融合算法集成
- 集成process_multi_animal_fusion调用
- 实现结果处理逻辑
- 错误处理机制

### 7.3 第三阶段: 循环发送实现
- 实现多动物循环发送
- 保持重复发送防护
- 完善调试信息

### 7.4 第四阶段: 功能保持
- 保持激光笔控制
- 兼容性验证
- 性能优化

### 7.5 第五阶段: 测试验证
- 编译测试
- 功能验证
- 性能测试

## 8. 结论

本方案设计完整可行，具备以下特点:
- **技术可行**: 基于现有算法框架，实现难度低
- **架构兼容**: 完全兼容现有代码架构
- **功能完整**: 涵盖采样、融合、发送、控制等所有环节
- **风险可控**: 风险评估充分，缓解措施完善

**预期效果**: 显著提升多动物识别的准确性和完整性，实现一个方格内多种动物的稳定识别和发送。

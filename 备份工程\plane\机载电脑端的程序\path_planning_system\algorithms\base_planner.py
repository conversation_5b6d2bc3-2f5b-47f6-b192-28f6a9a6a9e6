#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
路径规划算法基类
版权信息：米醋电子工作室
创建日期：2025-07-30

功能描述：
定义路径规划算法的统一接口，所有算法都必须继承此基类。
提供通用的性能监控和结果封装功能。
"""

import time
import psutil
import numpy as np
from abc import ABC, abstractmethod
from typing import List, Tuple, Optional

# 使用绝对导入避免相对导入问题
import sys
import os

# 添加核心模块路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
core_dir = os.path.join(parent_dir, 'core')
sys.path.insert(0, core_dir)

try:
    from grid_map import GridMap
    from path_result import PathResult
except ImportError:
    # 如果直接导入失败，使用动态导入
    import importlib.util

    spec = importlib.util.spec_from_file_location("grid_map", os.path.join(core_dir, "grid_map.py"))
    grid_map_module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(grid_map_module)
    GridMap = grid_map_module.GridMap

    spec = importlib.util.spec_from_file_location("path_result", os.path.join(core_dir, "path_result.py"))
    path_result_module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(path_result_module)
    PathResult = path_result_module.PathResult

class BasePlanner(ABC):
    """
    路径规划算法基类
    
    所有路径规划算法都必须继承此类并实现plan_path方法。
    提供统一的接口和性能监控功能。
    """
    
    def __init__(self, algorithm_name: str):
        """
        初始化规划器
        
        参数:
            algorithm_name: 算法名称
        """
        self.algorithm_name = algorithm_name
        self.grid_map: Optional[GridMap] = None
        self.start_pos: Optional[Tuple[int, int]] = None
        self.goal_pos: Optional[Tuple[int, int]] = None
        
        # 性能监控
        self._process = psutil.Process()
        self._start_memory = 0
        self._start_time = 0
    
    @abstractmethod
    def _plan_path_impl(self, grid_map: GridMap, start: Tuple[int, int]) -> List[Tuple[int, int]]:
        """
        算法具体实现（子类必须实现）

        参数:
            grid_map: 网格地图
            start: 起始位置 (row, col)

        返回:
            List[Tuple[int, int]]: 遍历所有可访问点的路径序列
        """
        pass
    
    def plan_path(self, grid_map: GridMap, start: Tuple[int, int]) -> PathResult:
        """
        执行全点遍历路径规划

        参数:
            grid_map: 网格地图
            start: 起始位置 (row, col)

        返回:
            PathResult: 包含遍历所有可访问点的路径规划结果
        """
        # 创建结果对象
        result = PathResult(self.algorithm_name)

        # 参数验证
        if not self._validate_inputs(grid_map, start):
            return result  # 返回失败结果

        # 保存参数
        self.grid_map = grid_map
        self.start_pos = start
        
        # 开始性能监控
        self._start_performance_monitoring()
        result.start_timing()
        
        try:
            # 执行算法
            grid_path = self._plan_path_impl(grid_map, start)

            # 结束计时
            result.end_timing()
            self._end_performance_monitoring(result)
            
            # 处理结果
            if grid_path:
                result.set_grid_path(grid_path)
                
                # 转换为position_code序列
                position_codes = []
                for row, col in grid_path:
                    position_code = grid_map.grid_to_position_code(row, col)
                    if position_code > 0:
                        position_codes.append(position_code)
                
                result.set_path_sequence(position_codes)
            
        except Exception as e:
            result.end_timing()
            self._end_performance_monitoring(result)
            print(f"路径规划算法 {self.algorithm_name} 执行失败: {e}")
        
        return result
    
    def _validate_inputs(self, grid_map: GridMap, start: Tuple[int, int]) -> bool:
        """
        验证输入参数

        参数:
            grid_map: 网格地图
            start: 起始位置

        返回:
            bool: 验证是否通过
        """
        if grid_map is None:
            print(f"{self.algorithm_name}: 网格地图不能为空")
            return False

        if not grid_map.is_valid_position(start[0], start[1]):
            print(f"{self.algorithm_name}: 起始位置无效或被禁飞区阻挡")
            return False

        # 检查是否有足够的可访问点进行遍历
        accessible_points = self._count_accessible_points(grid_map)
        if accessible_points < 10:  # 至少需要10个可访问点才有意义
            print(f"{self.algorithm_name}: 可访问点太少({accessible_points}个)，无法进行有效遍历")
            return False

        return True

    def _count_accessible_points(self, grid_map: GridMap) -> int:
        """计算可访问点的数量"""
        count = 0
        for row in range(GridMap.GRID_ROWS):
            for col in range(GridMap.GRID_COLS):
                if grid_map.is_valid_position(row, col):
                    count += 1
        return count
    
    def _start_performance_monitoring(self):
        """开始性能监控"""
        self._start_memory = self._process.memory_info().rss / 1024 / 1024  # MB
        self._start_time = time.perf_counter()
    
    def _end_performance_monitoring(self, result: PathResult):
        """结束性能监控并记录结果"""
        end_memory = self._process.memory_info().rss / 1024 / 1024  # MB
        result.statistics.memory_usage_mb = max(0, end_memory - self._start_memory)
    
    def get_algorithm_info(self) -> dict:
        """
        获取算法信息
        
        返回:
            dict: 算法信息
        """
        return {
            'name': self.algorithm_name,
            'type': self.__class__.__name__,
            'description': self.__doc__ or "无描述",
            'optimal': self._is_optimal(),
            'complexity': self._get_complexity()
        }
    
    def _is_optimal(self) -> bool:
        """算法是否保证最优解（子类可重写）"""
        return False
    
    def _get_complexity(self) -> str:
        """获取算法复杂度描述（子类可重写）"""
        return "未知"
    
    def calculate_heuristic(self, pos1: Tuple[int, int], pos2: Tuple[int, int]) -> float:
        """
        计算启发式距离（曼哈顿距离）
        
        参数:
            pos1, pos2: 网格坐标
            
        返回:
            float: 启发式距离
        """
        return abs(pos1[0] - pos2[0]) + abs(pos1[1] - pos2[1])
    
    def calculate_euclidean_distance(self, pos1: Tuple[int, int], pos2: Tuple[int, int]) -> float:
        """
        计算欧几里得距离
        
        参数:
            pos1, pos2: 网格坐标
            
        返回:
            float: 欧几里得距离
        """
        dx = pos1[0] - pos2[0]
        dy = pos1[1] - pos2[1]
        return np.sqrt(dx * dx + dy * dy)
    
    def reconstruct_path(self, came_from: dict, current: Tuple[int, int]) -> List[Tuple[int, int]]:
        """
        重构路径（通用方法）
        
        参数:
            came_from: 父节点映射
            current: 当前节点
            
        返回:
            List[Tuple[int, int]]: 重构的路径
        """
        path = [current]
        while current in came_from:
            current = came_from[current]
            path.append(current)
        path.reverse()
        return path

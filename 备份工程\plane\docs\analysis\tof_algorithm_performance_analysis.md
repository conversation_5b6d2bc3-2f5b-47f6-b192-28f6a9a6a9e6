# TOF传感器算法性能瓶颈分析报告
**版权：米醋电子工作室**  
**分析日期：2024年**  
**目标平台：STM32F429 (168MHz ARM Cortex-M4)**  
**实时要求：1ms任务周期**

## 🎯 分析目标

1. **CPU周期分析**：量化关键算法的CPU消耗
2. **实时性评估**：确保1ms任务周期要求
3. **瓶颈识别**：找出性能关键路径
4. **优化方案**：设计高效替代算法

## 📊 关键算法性能分析

### 1. 冒泡排序算法分析

#### 当前实现
```c
void tof_sort_array(uint16_t *array, uint8_t length)
{
    for (uint8_t i = 0; i < length - 1; i++)
    {
        for (uint8_t j = 0; j < length - 1 - i; j++)
        {
            if (array[j] > array[j + 1])
            {
                uint16_t temp = array[j];
                array[j] = array[j + 1];
                array[j + 1] = temp;
            }
        }
    }
}
```

#### 性能分析
- **时间复杂度**：O(n²)
- **最坏情况**：64像素需要 64×63/2 = 2016次比较
- **CPU周期估算**：
  - 每次比较：约8个CPU周期（加载、比较、条件跳转）
  - 每次交换：约12个CPU周期（3次内存访问）
  - 最坏情况总计：2016×8 + 2016×12 = **40,320个CPU周期**
  - @168MHz：约**240μs**

#### 调用频率分析
```c
// 在tof_calculate_distance中被调用
case TOF_FILTER_MEDIAN:
    tof_sort_array(valid_distances, valid_count);  // 中位数滤波

case TOF_FILTER_ROBUST_AVG:
    tof_sort_array(valid_distances, valid_count);  // 鲁棒平均滤波
```

### 2. 状态机复杂度分析

#### 当前实现
```c
void TOF_RecvOneByte(uint8_t link_type, uint8_t byte)
{
    static uint8_t state = 0;
    // ... 其他静态变量
    
    switch (state)
    {
        case 0: // 等待帧头
        case 1: // 功能码
        case 2: // Reserved字节
        case 3: // 传感器ID
        case 4: // 系统时间戳(4字节)
        case 5: // Zone Map字节
        case 6: // 像素数据接收
        case 7: // 校验和验证
        default:
    }
}
```

#### 性能分析
- **状态数量**：8个状态（0-7 + default）
- **每字节处理**：
  - Switch语句：约4-6个CPU周期
  - 状态处理逻辑：约10-20个CPU周期
  - 数据缓冲：约8个CPU周期
- **单字节处理**：约**22-34个CPU周期**
- **完整帧处理**：400字节×30周期 = **12,000个CPU周期**
- @168MHz：约**71μs**

### 3. 像素数据处理分析

#### 当前实现
```c
// 每像素占6字节：距离3字节+状态1字节+信号强度2字节
for (uint8_t i = 0; i < pixel_count; i++)
{
    // 距离数据转换
    int32_t raw_distance_um = tof_convert_distance_raw(&pixel_data[i * 6]);
    pixel->distance_cm = (uint16_t)(raw_distance_um / 10000);
    
    // 状态码和信号强度
    pixel->status = pixel_data[i * 6 + 3];
    pixel->signal_strength = (uint16_t)pixel_data[i * 6 + 4] | 
                            ((uint16_t)pixel_data[i * 6 + 5] << 8);
    
    // 判断像素有效性
    pixel->is_valid = tof_is_pixel_valid(pixel->distance_cm, 
                                        pixel->signal_strength, pixel->status);
}
```

#### 性能分析
- **64像素处理**：
  - 距离转换：64×15周期 = 960周期
  - 数据提取：64×8周期 = 512周期
  - 有效性判断：64×20周期 = 1280周期
- **总计**：约**2752个CPU周期**
- @168MHz：约**16.4μs**

## 🚀 性能瓶颈总结

### CPU时间分布（单次完整处理）
| 组件 | CPU周期 | 时间@168MHz | 占比 |
|------|---------|-------------|------|
| 冒泡排序 | 40,320 | 240μs | 73.2% |
| 状态机处理 | 12,000 | 71μs | 21.8% |
| 像素数据处理 | 2,752 | 16.4μs | 5.0% |
| **总计** | **55,072** | **327.4μs** | **100%** |

### 实时性评估
- **1ms任务周期占用率**：327.4μs / 1000μs = **32.7%**
- **剩余CPU时间**：672.6μs（67.3%）
- **评估结果**：当前实现满足1ms实时要求，但优化空间巨大

## 🎯 优化方案设计

### 1. 排序算法优化

#### 方案A：插入排序替代（已实现）
```c
static void tof_sort_array_optimized(uint16_t *array, uint8_t length)
{
    for (uint8_t i = 1; i < length; i++)
    {
        uint16_t key = array[i];
        int8_t j = i - 1;
        
        while (j >= 0 && array[j] > key)
        {
            array[j + 1] = array[j];
            j--;
        }
        array[j + 1] = key;
    }
}
```

**性能提升**：
- **时间复杂度**：O(n) 平均情况，O(n²) 最坏情况
- **实际数据**：TOF数据通常部分有序，插入排序表现优异
- **CPU周期**：平均约8,000周期（80%减少）
- **时间节省**：约192μs

#### 方案B：快速选择算法（中位数专用）
```c
static uint16_t tof_quick_select_median(uint16_t *array, uint8_t length)
{
    // 仅用于中位数查找，无需完整排序
    // 时间复杂度：O(n) 平均情况
}
```

**性能提升**：
- **CPU周期**：约2,000周期（95%减少）
- **适用场景**：仅中位数滤波

### 2. 状态机简化优化

#### 简化方案：3状态状态机
```c
typedef enum {
    TOF_STATE_HEADER = 0,    // 等待帧头和基本信息
    TOF_STATE_DATA,          // 数据接收
    TOF_STATE_CHECKSUM       // 校验和验证
} tof_simple_state_t;
```

**优化实现**：
```c
void TOF_RecvOneByte_Optimized(uint8_t byte)
{
    static uint8_t state = TOF_STATE_HEADER;
    static uint16_t data_index = 0;
    static uint16_t expected_length = 0;
    
    switch (state)
    {
        case TOF_STATE_HEADER:
            // 合并帧头、功能码、传感器ID等处理
            break;
        case TOF_STATE_DATA:
            // 统一数据接收处理
            break;
        case TOF_STATE_CHECKSUM:
            // 校验和验证
            break;
    }
}
```

**性能提升**：
- **状态减少**：8状态→3状态
- **CPU周期**：约6,000周期（50%减少）
- **时间节省**：约36μs

### 3. 内联函数优化

#### 关键函数内联
```c
static inline bool tof_is_pixel_valid_fast(uint16_t distance, 
                                          uint16_t signal, uint8_t status)
{
    return (distance >= TOF_MIN_RANGE_CM && distance <= TOF_MAX_RANGE_CM && 
            signal >= TOF_MIN_SIGNAL_STRENGTH && 
            (status == TOF_STATUS_VALID || status == TOF_STATUS_VALID_NO_PREV));
}

static inline uint16_t tof_convert_distance_fast(uint8_t *bytes)
{
    // 优化的距离转换，减少除法运算
    int32_t temp = (int32_t)((bytes[0] << 8) | (bytes[1] << 16) | (bytes[2] << 24)) / 256;
    return (uint16_t)(temp / 10000);  // 可优化为位移运算
}
```

**性能提升**：
- **函数调用开销**：消除约500个CPU周期
- **时间节省**：约3μs

## 📈 综合优化效果预估

### 优化前后对比
| 组件 | 优化前(μs) | 优化后(μs) | 节省(μs) | 提升率 |
|------|------------|------------|----------|--------|
| 排序算法 | 240 | 48 | 192 | 80% |
| 状态机 | 71 | 35 | 36 | 51% |
| 像素处理 | 16.4 | 13.4 | 3 | 18% |
| **总计** | **327.4** | **96.4** | **231** | **70.6%** |

### 实时性能提升
- **优化后1ms占用率**：96.4μs / 1000μs = **9.6%**
- **CPU时间释放**：231μs（23.1%的CPU时间）
- **实时裕量提升**：从67.3%提升到90.4%

### STM32F429适配性
- **更好的中断响应**：更多CPU时间用于其他任务
- **功耗优化**：减少CPU活跃时间
- **系统稳定性**：更大的实时裕量

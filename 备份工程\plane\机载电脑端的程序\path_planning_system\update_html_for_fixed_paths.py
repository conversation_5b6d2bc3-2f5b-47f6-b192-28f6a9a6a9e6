#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
更新修正路径的HTML可视化
版权信息：米醋电子工作室
创建日期：2025-07-31
作者：Emma (产品经理)
编码格式：UTF-8

功能描述：
为修正后的安全路径重新生成HTML可视化页面
"""

import json
import os
from typing import List, Dict, Tuple

class HTMLUpdater:
    """HTML更新器"""
    
    def __init__(self):
        """初始化更新器"""
        self.grid_rows = 7  # B1-B7
        self.grid_cols = 9  # A1-A9
        
    def position_code_to_grid(self, position_code: int) -> Tuple[int, int]:
        """position_code转网格坐标"""
        if position_code < 11 or position_code > 97:
            return (-1, -1)
        
        col_num = position_code // 10  # A列号 (1-9)
        row_num = position_code % 10   # B行号 (1-7)
        
        if col_num < 1 or col_num > 9 or row_num < 1 or row_num > 7:
            return (-1, -1)
        
        row = row_num - 1  # B1->0, B2->1, ..., B7->6
        col = col_num - 1  # A1->0, A2->1, ..., A9->8
        
        return (row, col)
    
    def generate_path_html(self, path_data: Dict, path_index: int) -> str:
        """生成单个路径的HTML页面"""
        no_fly_zones = path_data['no_fly_zones']
        patrol_path = path_data['patrol_path_sequence']
        return_path = path_data['return_path_sequence']
        
        # 创建网格状态矩阵
        grid_status = [['empty' for _ in range(self.grid_cols)] for _ in range(self.grid_rows)]
        grid_content = [['&nbsp;' for _ in range(self.grid_cols)] for _ in range(self.grid_rows)]
        
        # 标记禁飞区
        for zone in no_fly_zones:
            row, col = self.position_code_to_grid(zone)
            if row != -1 and col != -1:
                grid_status[row][col] = 'no-fly'
                grid_content[row][col] = '🚫'
        
        # 标记巡查路径
        for i, pos in enumerate(patrol_path):
            row, col = self.position_code_to_grid(pos)
            if row != -1 and col != -1:
                if grid_status[row][col] != 'no-fly':
                    grid_status[row][col] = 'patrol'
                    grid_content[row][col] = str(i + 1)
        
        # 标记返航路径
        for i, pos in enumerate(return_path):
            row, col = self.position_code_to_grid(pos)
            if row != -1 and col != -1:
                if grid_status[row][col] != 'no-fly':
                    if grid_status[row][col] == 'patrol':
                        grid_content[row][col] += f'→R{i + 1}'
                    else:
                        grid_status[row][col] = 'return'
                        grid_content[row][col] = f'R{i + 1}'
        
        # 标记起点
        start_row, start_col = self.position_code_to_grid(91)  # A9B1
        if start_row != -1 and start_col != -1:
            grid_content[start_row][start_col] = '🏠START'
        
        # 生成HTML内容
        html_content = f'''<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>路径{path_index} - 禁飞区{no_fly_zones}</title>
    <style>
        body {{
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }}
        .header {{
            text-align: center;
            margin-bottom: 20px;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }}
        .path-info {{
            display: flex;
            justify-content: space-around;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }}
        .info-card {{
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin: 5px;
            min-width: 150px;
        }}
        .info-card h3 {{
            margin: 0 0 10px 0;
            color: #333;
            font-size: 14px;
        }}
        .info-card .value {{
            font-size: 18px;
            font-weight: bold;
            color: #667eea;
        }}
        .grid-container {{
            display: flex;
            justify-content: center;
            margin: 20px 0;
        }}
        .path-grid {{
            border-collapse: collapse;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }}
        .path-grid td {{
            width: 60px;
            height: 60px;
            border: 1px solid #ddd;
            text-align: center;
            vertical-align: middle;
            font-size: 12px;
            font-weight: bold;
            position: relative;
        }}
        .empty {{ background-color: #f8f9fa; }}
        .no-fly {{ 
            background-color: #ff6b6b; 
            color: white;
            font-size: 16px;
        }}
        .patrol {{ 
            background-color: #4ecdc4; 
            color: white;
        }}
        .return {{ 
            background-color: #45b7d1; 
            color: white;
        }}
        .legend {{
            display: flex;
            justify-content: center;
            margin: 20px 0;
            flex-wrap: wrap;
        }}
        .legend-item {{
            display: flex;
            align-items: center;
            margin: 5px 15px;
            padding: 8px 12px;
            background: white;
            border-radius: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }}
        .legend-color {{
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin-right: 8px;
        }}
        .navigation {{
            text-align: center;
            margin: 20px 0;
        }}
        .nav-button {{
            display: inline-block;
            padding: 10px 20px;
            margin: 0 10px;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            transition: background 0.3s;
        }}
        .nav-button:hover {{
            background: #5a67d8;
        }}
        .safety-notice {{
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            text-align: center;
        }}
        .safety-notice.updated {{
            background: #fff3cd;
            border-color: #ffeaa7;
            color: #856404;
        }}
    </style>
</head>
<body>
    <div class="header">
        <h1>🛩️ 无人机路径规划可视化</h1>
        <h2>路径 {path_index} - 禁飞区 {no_fly_zones}</h2>
        <p>米醋电子工作室 | 安全优化版本</p>
    </div>
    
    <div class="safety-notice updated">
        <strong>⚠️ 安全更新通知</strong><br>
        此路径已根据飞机尺寸安全要求进行优化，确保所有移动都符合安全标准。
    </div>
    
    <div class="path-info">
        <div class="info-card">
            <h3>禁飞区</h3>
            <div class="value">{", ".join(map(str, no_fly_zones))}</div>
        </div>
        <div class="info-card">
            <h3>巡查长度</h3>
            <div class="value">{len(patrol_path)} 点</div>
        </div>
        <div class="info-card">
            <h3>返航长度</h3>
            <div class="value">{len(return_path)} 点</div>
        </div>
        <div class="info-card">
            <h3>总飞行时间</h3>
            <div class="value">{path_data.get('total_flight_time', 0):.0f} 秒</div>
        </div>
        <div class="info-card">
            <h3>覆盖率</h3>
            <div class="value">{path_data.get('coverage_rate', 100):.1f}%</div>
        </div>
    </div>
    
    <div class="grid-container">
        <table class="path-grid">'''
        
        # 生成网格
        for row in range(self.grid_rows):
            html_content += '\n            <tr>'
            for col in range(self.grid_cols):
                css_class = grid_status[row][col]
                content = grid_content[row][col]
                html_content += f'\n                <td class="{css_class}">{content}</td>'
            html_content += '\n            </tr>'
        
        html_content += f'''
        </table>
    </div>
    
    <div class="legend">
        <div class="legend-item">
            <div class="legend-color" style="background-color: #ff6b6b;"></div>
            <span>禁飞区 🚫</span>
        </div>
        <div class="legend-item">
            <div class="legend-color" style="background-color: #4ecdc4;"></div>
            <span>巡查路径 (数字=顺序)</span>
        </div>
        <div class="legend-item">
            <div class="legend-color" style="background-color: #45b7d1;"></div>
            <span>返航路径 (R+数字)</span>
        </div>
        <div class="legend-item">
            <div class="legend-color" style="background-color: #f8f9fa; border: 1px solid #ddd;"></div>
            <span>可飞行区域</span>
        </div>
    </div>
    
    <div class="navigation">
        <a href="visualization_index.html" class="nav-button">🏠 返回首页</a>
        <a href="path_{path_index-1:03d}.html" class="nav-button">⬅️ 上一个</a>
        <a href="path_{path_index+1:03d}.html" class="nav-button">➡️ 下一个</a>
    </div>
    
    <div class="safety-notice">
        <strong>✅ 安全认证</strong><br>
        此路径已通过飞机尺寸安全验证，所有对角线移动都避开了禁飞区边界。
    </div>
</body>
</html>'''
        
        return html_content
    
    def update_specific_paths(self, path_numbers: List[int]):
        """更新指定路径的HTML文件"""
        print(f"🔄 更新指定路径的HTML可视化")
        print(f"需要更新的路径: {path_numbers}")
        print("=" * 50)
        
        # 加载修正后的数据
        with open('safe_optimized_return_paths.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        path_data_list = data['path_data']
        
        updated_count = 0
        
        for path_num in path_numbers:
            if 1 <= path_num <= len(path_data_list):
                path_data = path_data_list[path_num - 1]  # 转换为0基索引
                
                # 生成HTML内容
                html_content = self.generate_path_html(path_data, path_num)
                
                # 保存HTML文件
                filename = f'path_{path_num:03d}.html'
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(html_content)
                
                print(f"✅ 已更新: {filename}")
                updated_count += 1
            else:
                print(f"❌ 路径编号无效: {path_num}")
        
        print(f"\n📊 更新完成:")
        print(f"   成功更新: {updated_count}/{len(path_numbers)} 个文件")
        
        return updated_count

def main():
    """主函数"""
    print("🚀 HTML可视化更新工具")
    print("=" * 70)
    print("版权信息：米醋电子工作室")
    print("创建日期：2025-07-31")
    print("作者：Emma (产品经理)")
    print("=" * 70)
    
    updater = HTMLUpdater()
    
    # 更新修正过的路径：50, 52, 90, 92
    fixed_paths = [50, 52, 90, 92]
    
    updated_count = updater.update_specific_paths(fixed_paths)
    
    print(f"\n🎉 HTML更新完成!")
    print(f"✅ 已更新 {updated_count} 个HTML文件")
    print(f"✅ 所有修正路径的可视化已同步更新")
    print(f"✅ 新的HTML文件包含安全优化标识")

if __name__ == "__main__":
    main()

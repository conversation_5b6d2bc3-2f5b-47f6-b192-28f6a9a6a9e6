#ifndef __PID_H
#define __PID_H
#include "SysConfig.h"
#include <stdlib.h>
#include <Math.h>
#include <stdbool.h>
// ================== 位置控制相关变量 ==================
extern s16 error_pos[4],target_pos[4];
extern s16 error_body[2];
extern float PID_V[4];
extern u8 flag_Control[4];
extern s16 work_pos[63][6];
extern s16 actual_work_pos[63][4];
extern s16 target_g_port[2];
// ================== 激光控制相关变量 ==================
extern s16 target_laser_pixel[2];    // 激光目标像素位置 [X, Y]
extern s16 error_laser[2];           // 激光像素误差 [X, Y]
extern s16 laser_V[2];               // 激光PID输出的角度调整量 [X, Y] (0.01度单位)
extern u8 flag_laser;                // 激光控制使能标志

// ================== 摄像头控制相关变量 ==================
extern s16 target_cam[2];            // 摄像头目标位置 [X, Y]
extern s16 error_cam[2];             // 摄像头误差 [X, Y]
extern float cam_V[2];               // 摄像头PID输出 [X, Y]
extern u8 flag_cam;                  // 摄像头控制标志

// ================== 视觉PID控制相关变量 ==================
extern float visual_offset_x, visual_offset_y; // 当前视觉偏移量
extern u8 flag_visual_pid;                     // 视觉PID控制标志
extern u32 visual_data_last_update_ms;         // 视觉数据最后更新时间

typedef struct
{
    float Val_obj;                          // 4字节 - 目标值
    float Val_cur;                          // 4字节 - 当前值 (未使用)
    float ts;                               // 4字节 - 采样时间
    float Kp, Ki, Kd;                       // 12字节 - PID参数
    float fLower_Limit_Output;              // 4字节 - 输出下限
    float fUpper_Limit_Output;              // 4字节 - 输出上限
    float fLower_Limit_Integral;            // 4字节 - 积分下限
    float fUpper_Limit_Integral;            // 4字节 - 积分上限
    float fLimit_Derivat;                   // 4字节 - 微分限幅
    float fIntegral;                        // 4字节 - 积分累积量
    float fPreviousError;                   // 4字节 - 上次误差
    float fStab;                            // 4字节 - 前馈系数
    float Kd_d;                             // 4字节 - 二阶微分系数 (未使用)
    float fLimit_DDerivate;                 // 4字节 - 二阶微分限幅 (未使用)
    float ferror_death;                     // 4字节 - 死区
    float beta_Integral;                    // 4字节 - 积分衰减系数
    float ARange_error_Integral;            // 4字节 - 误差范围A (未使用)
    float BRange_error_Integral;            // 4字节 - 误差范围B (未使用)
    float fIntegral_Separation_Threshold;   // 4字节 - 积分分离阈值
    uint8_t integral_separation_mode;       // 1字节 -  积分分离模式：0=保持，1=清零，2=衰减
} PID_Struct_f;

// 积分分离模式定义
#define INTEGRAL_MODE_KEEP    0    // 保持积分量不变（推荐）
#define INTEGRAL_MODE_CLEAR   1    // 清零积分量（快速响应）
#define INTEGRAL_MODE_DECAY   2    // 衰减积分量（折中方案）

// 函数声明
void OutLoop_Control_Z(void);
void OutLoop_Control_XY(void);
void OutLoop_Control_yaw(void);
void OutLoop_Control_cam(void);
void OutLoop_Control_g_port(void);
void PID_Init(void);

// ========== FLAG控制函数 ==========
void XY_flag_Control(u8 flag1, u8 flag2);
void all_flag_reset(void);
void YAW_flag_Control(u8 flag);
void Z_flag_Control(u8 flag);
void cam_flag_Control(u8 flag);
void laser_flag_Control(u8 flag);
void g_port_flag_Control(u8 flag);

// ========== 视觉PID控制函数 ==========
void Enhanced_Visual_Control(void);
void visual_pid_flag_Control(u8 flag);
void Visual_Get_Offset(float *offset_x, float *offset_y);
void Visual_Debug_Info(void);
bool Visual_Parameter_Tuning(float kp, float ki, float kd);
bool is_visual_data_valid(void);
void handle_visual_data_loss(void);
void limit_visual_offset(void);
void limit_visual_offset_values(void);
void limit_target_position(void);


void Laser_Set_Target_Pixel(s16 target_x, s16 target_y);
void Laser_Set_Target_Center(void);
s16 Laser_Get_Pixel_Error_X(void);
s16 Laser_Get_Pixel_Error_Y(void);
u8 Laser_Is_On_Target(s16 tolerance);
float PID_Calc(PID_Struct_f* pPID, float ferror);

// ================== 激光跟踪调试和监控函数 ==================
void Laser_System_Test_And_Verification(void);               // 系统测试验证函数
void Laser_Parameter_Tuning_Guide(void);                     // 参数调优指南函数
s16 Laser_Get_Accumulated_Yaw(void);                         // 获取累积YAW目标位置（串口调试用）
s16 Laser_Get_Accumulated_Pitch(void);                       // 获取累积PITCH目标位置（串口调试用）
float Laser_Get_Filtered_Yaw(void);                          // 获取滤波后YAW位置（串口调试用）
float Laser_Get_Filtered_Pitch(void);                        // 获取滤波后PITCH位置（串口调试用）
void Laser_Reset_Accumulated_Position(void);                 // 复位累积位置（调试用）

// ================== PID参数验证和调试函数 ==================
void PID_Parameters_Verification(void);                      // 验证PID参数宏定义值的正确性
void Get_PID_Parameter_Values(void);                         // 获取当前PID参数值（串口调试用）

extern s16 target_g_port[2];

// ================== 激光跟踪累积目标位置全局变量声明 ==================
extern s16 target_yaw_accumulated;          // 累积目标YAW位置（可通过串口发送到上位机）
extern s16 target_pitch_accumulated;        // 累积目标PITCH位置（可通过串口发送到上位机）
extern u8 target_initialized;               // 目标位置初始化标志
extern u8 filter_initialized;               // 滤波器初始化标志
extern float filtered_yaw;                  // 滤波后的YAW位置（便于调试观察）
extern float filtered_pitch;                // 滤波后的PITCH位置（便于调试观察）
extern u8 laser_reset_request;              // 激光控制状态复位请求标志

extern s16 target_laser_pixel[2];

// ================== 激光PID参数全局变量声明 ==================
extern PID_Struct_f X_PID;
extern PID_Struct_f Y_PID;
extern PID_Struct_f Z_PID;
extern PID_Struct_f Yaw_PID;
extern PID_Struct_f cam_PID;
extern PID_Struct_f g_port_PID;
extern PID_Struct_f laser_x_PID;    // 激光X轴PID
extern PID_Struct_f laser_y_PID;    // 激光Y轴PID

#endif // __PID_H

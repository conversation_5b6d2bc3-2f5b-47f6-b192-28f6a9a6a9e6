# A面定点巡查路径规划修复 PRD

## 1. 文档信息

| 项目 | 内容 |
|------|------|
| 文档版本 | v1.0 |
| 创建日期 | 2025-07-22 |
| 负责人 | Emma (产品经理) |
| 项目类型 | Bug修复 |
| 优先级 | 高 |

## 2. 背景与问题陈述

### 2.1 问题背景
在飞控系统的定点巡查功能中，发现A面4号位置（face_id=1, position_id=4）的路径规划存在逻辑错误。

### 2.2 具体问题
**当前错误行为**：
- 飞机能够正确飞往A面4号位置并完成巡检
- 巡检完成后，飞机直接飞往D面中转点（X=350, Y=0）
- 跳过了应有的"返回A面中转点"步骤

**预期正确行为**：
根据QR_FACE_A的路径规划逻辑，应按以下4步执行：
1. A面中转点 → 目标点（已正确执行）
2. 目标点 → A面中转点（被跳过，这是问题所在）
3. A面中转点 → D面中转点
4. D面中转点 → 终点

### 2.3 影响范围
- 影响A面所有6个位置的定点巡查功能
- 可能导致路径规划不一致，影响飞行安全
- 与B/C/D面的路径规划逻辑不统一

## 3. 目标与成功指标

### 3.1 项目目标 (Objectives)
- **主要目标**：修复A面路径规划逻辑，确保完整4步路径执行
- **次要目标**：保持与其他面路径规划的一致性
- **质量目标**：不影响现有B/C/D面功能

### 3.2 关键结果 (Key Results)
- KR1：A面所有6个位置都能正确执行完整4步路径
- KR2：修复后编译无错误，功能测试通过率100%
- KR3：B/C/D面路径规划功能不受影响

### 3.3 反向指标 (Counter Metrics)
- 不增加代码复杂度
- 不影响系统性能
- 不破坏现有接口

## 4. 用户画像与用户故事

### 4.1 目标用户
- **主要用户**：飞控系统操作员
- **次要用户**：系统维护工程师
- **受益用户**：比赛参与者

### 4.2 用户故事
**作为飞控系统操作员**，我希望A面定点巡查能够按照预定路径执行，以确保飞行安全和任务完成度。

**作为系统维护工程师**，我希望所有面的路径规划逻辑保持一致，便于系统维护和故障排查。

## 5. 功能规格详述

### 5.1 核心修复内容
**文件位置**：`plane\FcSrc\User\zigbee.c`
**修复行号**：第542行
**修复内容**：
```c
// 修改前（错误）：
transit_point = 0;  // A面中转点索引（需要根据实际情况设置）

// 修改后（正确）：
transit_point = 6;  // A面中转点索引
```

### 5.2 技术原理
根据`User_Task.c`中`work_pos`数组定义：
- `work_pos[6] = {0, 0, 100-17, 0}` → A面中转点
- `work_pos[0] = {0, 100-26, 100-17, 0}` → A面位置4检测点

错误设置导致路径状态机混乱，修复后将确保正确的路径流程。

### 5.3 路径状态机流程
修复后的A面路径规划将按以下状态执行：
1. **Step 2**：飞往A面中转点（索引6）
2. **Step 3**：飞往目标检测点（根据position_id确定）
3. **Step 4**：返回A面中转点（索引6）
4. **Step 6**：飞往D面中转点（索引21）
5. **Step 5**：飞往终点（索引28）

### 5.4 边缘情况处理
- 确保修改不影响B/C面（transit_point = 7）
- 确保修改不影响D面（transit_point = 21）
- 保持错误处理逻辑不变

## 6. 范围定义

### 6.1 包含功能 (In Scope)
- ✅ 修复zigbee.c第542行的transit_point设置
- ✅ 验证A面所有位置的路径规划
- ✅ 编译验证和功能测试
- ✅ 确保其他面功能不受影响

### 6.2 排除功能 (Out of Scope)
- ❌ 修改路径状态机的整体逻辑
- ❌ 修改工作点坐标定义
- ❌ 修改二维码管理系统
- ❌ 添加新的路径规划功能

## 7. 依赖与风险

### 7.1 内部依赖项
- 依赖现有的路径状态机逻辑
- 依赖work_pos数组的坐标定义
- 依赖QR_Code_Manager的映射关系

### 7.2 外部依赖项
- Keil编译器环境
- 飞控硬件测试环境

### 7.3 潜在风险
| 风险 | 概率 | 影响 | 缓解措施 |
|------|------|------|----------|
| 修改引入新bug | 低 | 中 | 充分测试，保留回滚方案 |
| 影响其他面功能 | 极低 | 高 | 回归测试验证 |
| 编译错误 | 极低 | 低 | 语法检查，增量编译 |

## 8. 发布初步计划

### 8.1 开发阶段
- **阶段1**：代码修复（预计30分钟）
- **阶段2**：编译验证（预计15分钟）
- **阶段3**：功能测试（预计45分钟）

### 8.2 测试策略
- **单元测试**：验证修改点的正确性
- **集成测试**：验证A面完整路径流程
- **回归测试**：验证其他面功能不受影响

### 8.3 发布标准
- 编译无错误和警告
- A面所有位置路径测试通过
- B/C/D面功能验证通过
- 代码审查通过

## 9. 验收标准

### 9.1 功能验收
- [ ] A面所有6个位置都能执行完整4步路径
- [ ] Step 4正确返回A面中转点（索引6）
- [ ] 路径状态机调试信息显示正确的transit_point值

### 9.2 质量验收
- [ ] 代码修改符合现有风格和规范
- [ ] 编译无错误和警告
- [ ] 不影响B/C/D面的现有功能

### 9.3 性能验收
- [ ] 修复不增加系统开销
- [ ] 路径规划效率保持不变

---

**文档状态**：已完成  
**下一步行动**：提交技术团队进行实施

.\build\datatransfer.o: ..\FcSrc\DataTransfer.c
.\build\datatransfer.o: ..\FcSrc\DataTransfer.h
.\build\datatransfer.o: ..\FcSrc\SysConfig.h
.\build\datatransfer.o: ..\DriversMcu\STM32F4xx\McuConfig.h
.\build\datatransfer.o: ..\DriversMcu\STM32F4xx\Libraries\CMSIS\ST\STM32F4xx\Include\stm32f4xx.h
.\build\datatransfer.o: ..\DriversMcu\STM32F4xx\Libraries\CMSIS\Include\core_cm4.h
.\build\datatransfer.o: D:\keil5\ARM\ARMCC\Bin\..\include\stdint.h
.\build\datatransfer.o: ..\DriversMcu\STM32F4xx\Libraries\CMSIS\Include\core_cmInstr.h
.\build\datatransfer.o: ..\DriversMcu\STM32F4xx\Libraries\CMSIS\Include\core_cmFunc.h
.\build\datatransfer.o: ..\DriversMcu\STM32F4xx\Libraries\CMSIS\Include\core_cmSimd.h
.\build\datatransfer.o: ..\DriversMcu\STM32F4xx\Libraries\CMSIS\ST\STM32F4xx\Include\system_stm32f4xx.h
.\build\datatransfer.o: ..\DriversMcu\STM32F4xx\Drivers\stm32f4xx_conf.h
.\build\datatransfer.o: ..\DriversMcu\STM32F4xx\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_adc.h
.\build\datatransfer.o: ..\DriversMcu\STM32F4xx\Libraries\CMSIS\ST\STM32F4xx\Include\stm32f4xx.h
.\build\datatransfer.o: ..\DriversMcu\STM32F4xx\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_dbgmcu.h
.\build\datatransfer.o: ..\DriversMcu\STM32F4xx\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_dma.h
.\build\datatransfer.o: ..\DriversMcu\STM32F4xx\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_exti.h
.\build\datatransfer.o: ..\DriversMcu\STM32F4xx\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_flash.h
.\build\datatransfer.o: ..\DriversMcu\STM32F4xx\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_gpio.h
.\build\datatransfer.o: ..\DriversMcu\STM32F4xx\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_i2c.h
.\build\datatransfer.o: ..\DriversMcu\STM32F4xx\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_pwr.h
.\build\datatransfer.o: ..\DriversMcu\STM32F4xx\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_rcc.h
.\build\datatransfer.o: ..\DriversMcu\STM32F4xx\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_spi.h
.\build\datatransfer.o: ..\DriversMcu\STM32F4xx\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_syscfg.h
.\build\datatransfer.o: ..\DriversMcu\STM32F4xx\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_tim.h
.\build\datatransfer.o: ..\DriversMcu\STM32F4xx\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_usart.h
.\build\datatransfer.o: ..\DriversMcu\STM32F4xx\Libraries\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_wwdg.h
.\build\datatransfer.o: ..\DriversMcu\STM32F4xx\Libraries\STM32F4xx_StdPeriph_Driver\inc\misc.h
.\build\datatransfer.o: ..\DriversBsp\Drv_BSP.h
.\build\datatransfer.o: ..\FcSrc\SysConfig.h
.\build\datatransfer.o: ..\DriversMcu\STM32F4xx\Drivers\Drv_Sys.h
.\build\datatransfer.o: ..\FcSrc\LX_LowLevelFunc.h
.\build\datatransfer.o: ..\FcSrc\AnoPTv8\AnoPTv8.h
.\build\datatransfer.o: D:\keil5\ARM\ARMCC\Bin\..\include\string.h
.\build\datatransfer.o: ..\FcSrc\AnoPTv8\AnoPTv8Run.h
.\build\datatransfer.o: ..\FcSrc\AnoPTv8\AnoPTv8.h
.\build\datatransfer.o: ..\FcSrc\AnoPTv8\AnoPTv8Par.h
.\build\datatransfer.o: ..\FcSrc\AnoPTv8\AnoPTv8Cmd.h
.\build\datatransfer.o: ..\FcSrc\AnoPTv8\AnoPTv8ExAPI.h
.\build\datatransfer.o: ..\FcSrc\AnoPTv8\AnoPTv8FrameFactory.h
.\build\datatransfer.o: ..\DriversMcu\STM32F4xx\Drivers\Drv_RcIn.h
.\build\datatransfer.o: ..\FcSrc\LX_ExtSensor.h
.\build\datatransfer.o: ..\DriversMcu\STM32F4xx\Drivers\Drv_led.h
.\build\datatransfer.o: ..\FcSrc\LX_FcState.h
.\build\datatransfer.o: ..\DriversMcu\STM32F4xx\Drivers\Drv_Uart.h
.\build\datatransfer.o: ..\FcSrc\User\mid360.h
.\build\datatransfer.o: D:\keil5\ARM\ARMCC\Bin\..\include\stdbool.h
.\build\datatransfer.o: ..\FcSrc\User\PID.h
.\build\datatransfer.o: D:\keil5\ARM\ARMCC\Bin\..\include\stdlib.h
.\build\datatransfer.o: D:\keil5\ARM\ARMCC\Bin\..\include\Math.h
.\build\datatransfer.o: ..\FcSrc\User\Maixcam.h
.\build\datatransfer.o: ..\FcSrc\User\tofmini.h
.\build\datatransfer.o: ..\FcSrc\User_Task.h

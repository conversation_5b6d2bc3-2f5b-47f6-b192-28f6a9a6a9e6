# 两点校准法调试增强修复报告

## 📋 问题描述

用户报告两点校准后飞行器出现"乱漂"现象，经分析发现需要增强调试信息以便更好地诊断校准过程。

## 🔍 问题分析

### 原始调试信息不足
```
11:12:31.662 #DB: -1#Calib P1 MID360 X: 
11:12:31.662 #DB: -1#Calib P1 MID360 Y: 
11:12:31.663 #DB: Calib point 1 set! 
11:13:17.208 #DB: 325#End X: 
11:13:17.209 #DB: 165#End Y: 
11:13:17.210 #DB: 0.930851#Scale X: 
11:13:17.211 #DB: 0.829876#Scale Y: 
11:13:17.212 #DB: 0#Offset X: 
11:13:17.213 #DB: 0#Offset Y: 
11:13:17.213 #DB: Two point calibration done!
```

### 缺失的关键信息
1. 第二个校准点的MID360坐标
2. 两点间的距离计算过程
3. 重复校准的防护机制
4. 校准参数重置功能

## 🛠️ 修复方案

### 1. 增强调试信息输出

**文件**: `plane/FcSrc/User/zigbee.c`

在`zigbee_execute_two_point_calibration`函数中添加详细的调试信息：

```c
// 输出第二个校准点信息供调试
AnoPTv8SendValStr(LT_D_IMU, ANOPTV8DEVID_SWJ, (float)mid360_x2, "Calib P2 MID360 X:");
AnoPTv8SendValStr(LT_D_IMU, ANOPTV8DEVID_SWJ, (float)mid360_y2, "Calib P2 MID360 Y:");
AnoPTv8SendValStr(LT_D_IMU, ANOPTV8DEVID_SWJ, mid360_dx, "MID360 DX:");
AnoPTv8SendValStr(LT_D_IMU, ANOPTV8DEVID_SWJ, mid360_dy, "MID360 DY:");
AnoPTv8SendValStr(LT_D_IMU, ANOPTV8DEVID_SWJ, actual_dx, "Actual DX:");
AnoPTv8SendValStr(LT_D_IMU, ANOPTV8DEVID_SWJ, actual_dy, "Actual DY:");
```

### 2. 防止重复校准

添加校准状态标志：
```c
static bool calibration_applied = false;   // 校准是否已应用标志
```

在`zigbee_apply_coordinate_calibration`函数中添加检查：
```c
// 防止重复应用校准
if(calibration_applied)
{
    AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_RED, "Calibration already applied!");
    return;
}
```

### 3. 校准重置功能

新增`zigbee_reset_coordinate_calibration`函数：
```c
void zigbee_reset_coordinate_calibration(void)
{
    coordinate_scale_x = 1.0f;
    coordinate_scale_y = 1.0f;
    coordinate_offset_x = 0;
    coordinate_offset_y = 0;
    calibration_applied = false;
    calib_point1_set = false;
    
    AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_GREEN, "Calibration reset!");
}
```

## 📊 增强后的调试信息

修复后的调试输出将包含：

```
Calib P1 MID360 X: -1
Calib P1 MID360 Y: -1
Calib point 1 set!
Calib P2 MID360 X: [第二个校准点X坐标]
Calib P2 MID360 Y: [第二个校准点Y坐标]
MID360 DX: [MID360坐标系距离X]
MID360 DY: [MID360坐标系距离Y]
Actual DX: [实际坐标系距离X]
Actual DY: [实际坐标系距离Y]
Scale X: [缩放系数X]
Scale Y: [缩放系数Y]
Offset X: [偏移量X]
Offset Y: [偏移量Y]
Coordinate calibration applied!
Start X: [起点校准后X坐标]
Start Y: [起点校准后Y坐标]
End X: [终点校准后X坐标]
End Y: [终点校准后Y坐标]
Two point calibration done!
```

## 🔧 文件修改清单

### 修改的文件
1. **plane/FcSrc/User/zigbee.c**
   - 添加详细调试信息输出
   - 增加重复校准防护
   - 新增校准重置函数

2. **plane/FcSrc/User/zigbee.h**
   - 添加`zigbee_reset_coordinate_calibration`函数声明

## 🎯 预期效果

1. **完整的校准过程可视化** - 能够看到完整的校准计算过程
2. **防止重复校准错误** - 避免在已校准的坐标上再次应用校准
3. **支持校准重置** - 可以重新进行校准操作
4. **更好的问题诊断** - 通过详细的调试信息快速定位问题

## 📝 使用说明

### 正常校准流程
1. 发送 `CMD=0x03, ID=3` 设置第一个校准点
2. 移动到第二个校准点
3. 发送 `CMD=0x03, ID=4` 执行校准
4. 观察详细的调试输出验证校准结果

### 重新校准流程
1. 调用 `zigbee_reset_coordinate_calibration()` 重置校准
2. 重新执行校准流程

## ⚠️ 注意事项

1. **坐标系理解** - (-1, -1)在用户坐标系下是正常值
2. **校准顺序** - 必须先设置第一个校准点再执行校准
3. **数据有效性** - 建议在校准前检查MID360数据有效性
4. **一次性校准** - 校准参数直接修改work_pos数组，建议谨慎使用

---

**修复完成时间**: 2025-01-15
**修复人员**: Alex (工程师)
**测试状态**: 待测试

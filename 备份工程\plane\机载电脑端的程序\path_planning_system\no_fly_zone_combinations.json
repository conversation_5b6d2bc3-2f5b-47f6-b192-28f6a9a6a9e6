{"metadata": {"generation_time": "2025-07-31", "total_combinations": 92, "valid_combinations": 92, "statistics": {"total_combinations": 92, "horizontal_combinations": 48, "vertical_combinations": 44, "theoretical_maximum": 94, "coverage_rate": 97.87234042553192}}, "combinations": [[11, 21, 31], [21, 31, 41], [31, 41, 51], [41, 51, 61], [51, 61, 71], [61, 71, 81], [12, 22, 32], [22, 32, 42], [32, 42, 52], [42, 52, 62], [52, 62, 72], [62, 72, 82], [72, 82, 92], [13, 23, 33], [23, 33, 43], [33, 43, 53], [43, 53, 63], [53, 63, 73], [63, 73, 83], [73, 83, 93], [14, 24, 34], [24, 34, 44], [34, 44, 54], [44, 54, 64], [54, 64, 74], [64, 74, 84], [74, 84, 94], [15, 25, 35], [25, 35, 45], [35, 45, 55], [45, 55, 65], [55, 65, 75], [65, 75, 85], [75, 85, 95], [16, 26, 36], [26, 36, 46], [36, 46, 56], [46, 56, 66], [56, 66, 76], [66, 76, 86], [76, 86, 96], [17, 27, 37], [27, 37, 47], [37, 47, 57], [47, 57, 67], [57, 67, 77], [67, 77, 87], [77, 87, 97], [11, 12, 13], [12, 13, 14], [13, 14, 15], [14, 15, 16], [15, 16, 17], [21, 22, 23], [22, 23, 24], [23, 24, 25], [24, 25, 26], [25, 26, 27], [31, 32, 33], [32, 33, 34], [33, 34, 35], [34, 35, 36], [35, 36, 37], [41, 42, 43], [42, 43, 44], [43, 44, 45], [44, 45, 46], [45, 46, 47], [51, 52, 53], [52, 53, 54], [53, 54, 55], [54, 55, 56], [55, 56, 57], [61, 62, 63], [62, 63, 64], [63, 64, 65], [64, 65, 66], [65, 66, 67], [71, 72, 73], [72, 73, 74], [73, 74, 75], [74, 75, 76], [75, 76, 77], [81, 82, 83], [82, 83, 84], [83, 84, 85], [84, 85, 86], [85, 86, 87], [92, 93, 94], [93, 94, 95], [94, 95, 96], [95, 96, 97]], "examples": {"horizontal_examples": [{"combination": [11, 21, 31], "description": "A1B1-A2B1-A3B1 (第一行左侧)", "visualization": "B1: [X][X][X][ ][ ][ ][ ][ ][ ]"}, {"combination": [71, 81, 91], "description": "A7B1-A8B1-A9B1 (第一行右侧，包含起点)", "note": "此组合无效，包含起点A9B1"}, {"combination": [44, 54, 64], "description": "A4B4-A5B4-A6B4 (中间行)", "visualization": "B4: [ ][ ][ ][X][X][X][ ][ ][ ]"}], "vertical_examples": [{"combination": [11, 12, 13], "description": "A1B1-A1B2-A1B3 (第一列下方)", "visualization": "A1列: B1[X] B2[X] B3[X] B4[ ] ..."}, {"combination": [95, 96, 97], "description": "A9B5-A9B6-A9B7 (第九列上方)", "visualization": "A9列: ... B5[X] B6[X] B7[X]"}, {"combination": [53, 54, 55], "description": "A5B3-A5B4-A5B5 (中间列)", "visualization": "A5列: ... B3[X] B4[X] B5[X] ..."}]}}
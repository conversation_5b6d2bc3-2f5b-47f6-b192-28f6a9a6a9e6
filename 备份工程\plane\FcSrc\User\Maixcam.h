#include "SysConfig.h"
#include "stm32f4xx.h"

// Maixcam协议数据结构
typedef struct
{
    s16 id;                   // 动物类型标识符 (1=象，2=虎，3=狼，4=猴，5=孔雀，0=普通坐标)
    s16 x;                   // X坐标 (16位有符号整数)
    s16 y;                   // Y坐标 (16位有符号整数)
    s16 count;                // 识别到的动物数量（当id为1-5时有效）
    u8 data_valid;           // 数据有效标志
    u32 last_update_ms;      // 最后更新时间戳 (毫秒)
} __attribute__((packed)) maixcam_info;

// 用于字节序转换的联合体
typedef union
{
    s16 coord;               // 16位有符号坐标值
    struct {
        u8 low;              // 低字节
        u8 high;             // 高字节
    } __attribute__((packed)) bytes;
} __attribute__((packed)) coord_union;

// 全局变量声明
extern maixcam_info maixcam;
// 函数声明
void maixcam_receiver_GetOneByte(const u8 linktype, const u8 data);  // Maixcam协议接收函数
void maixcam_send_data(u8 id, s16 x, s16 y, u8 count);              // Maixcam数据发送函数

// 数据访问函数
s16 maixcam_get_x(void);                                             // 获取X坐标
s16 maixcam_get_y(void);                                             // 获取Y坐标
u8 maixcam_get_count(void);                                          // 获取动物数量
u8 maixcam_is_data_valid(void);                                      // 检查数据有效性
u8 maixcam_get_id(void);                                             // 获取动物类型ID
void maixcam_clear_data(void);                                       // 清除MaixCam历史数据

// 野生动物巡查系统函数
// maixcam_calculate_position_code函数已移除 - 屏幕坐标无法直接转换为网格位置


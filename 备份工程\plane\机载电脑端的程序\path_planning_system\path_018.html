<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>路径18 - 禁飞区[53, 63, 73] (安全修正版)</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
        }
        .grid-container {
            display: flex;
            justify-content: center;
            margin: 30px 0;
        }
        .grid {
            display: grid;
            grid-template-columns: repeat(9, 50px);
            grid-template-rows: repeat(7, 50px);
            gap: 2px;
            border: 3px solid #333;
            background-color: #333;
            padding: 10px;
            border-radius: 10px;
        }
        .cell {
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 12px;
            color: white;
            border-radius: 4px;
            position: relative;
        }
        .normal { background-color: #4CAF50; }
        .no-fly { background-color: #f44336; }
        .start { background-color: #2196F3; }
        .patrol { background-color: #FF9800; }
        .return { background-color: #9C27B0; }
        .path-number {
            position: absolute;
            top: 2px;
            left: 2px;
            font-size: 8px;
            background: rgba(0,0,0,0.7);
            padding: 1px 3px;
            border-radius: 2px;
        }
        .info-panel {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 30px 0;
        }
        .info-box {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            border-left: 5px solid #007bff;
        }
        .legend {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        .legend-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .legend-color {
            width: 20px;
            height: 20px;
            border-radius: 4px;
        }
        .path-sequence {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.6;
        }
        .navigation {
            text-align: center;
            margin: 30px 0;
        }
        .nav-button {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            margin: 0 10px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
        }
        .nav-button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>路径18 可视化 (安全修正版)</h1>
            <p>禁飞区: [53, 63, 73] | 巡查点数: 60 | 返航点数: 7</p>
            <p>总飞行时间: 660.0秒 | 覆盖率: 100.0%</p>
        </div>

        <div class="legend">
            <div class="legend-item">
                <div class="legend-color start"></div>
                <span>起点/终点 (A9B1)</span>
            </div>
            <div class="legend-item">
                <div class="legend-color no-fly"></div>
                <span>禁飞区</span>
            </div>
            <div class="legend-item">
                <div class="legend-color patrol"></div>
                <span>巡查路径</span>
            </div>
            <div class="legend-item">
                <div class="legend-color return"></div>
                <span>返航路径</span>
            </div>
            <div class="legend-item">
                <div class="legend-color normal"></div>
                <span>正常区域</span>
            </div>
        </div>

        <div class="grid-container">
            <div class="grid" id="pathGrid">
                <!-- 网格将由JavaScript生成 -->
            </div>
        </div>

        <div class="info-panel">
            <div class="info-box">
                <h3>🎯 巡查路径序列 (60个点)</h3>
                <div class="path-sequence">
                    91 → 81 → 71 → 61 → 51 → 41 → 31 → 21 → 11 → 12 → 22 → 32 → 42 → 52 → 62 → 72 → 82 → 92 → 93 → 83 → 84 → 74 → 64 → 54 → 44 → 43 → 33 → 23 → 13 → 14 → 24 → 34 → 35 → 25 → 15 → 16 → 26 → 36 → 46 → 45 → 55 → 65 → 75 → 85 → 95 → 94 → 96 → 86 → 76 → 66 → 56 → 57 → 47 → 37 → 27 → 17 → 67 → 77 → 87 → 97
                </div>
            </div>
            <div class="info-box">
                <h3>🏠 返航路径序列 (7个点)</h3>
                <div class="path-sequence">
                    97 → 96 → 95 → 94 → 93 → 92 → 91
                </div>
            </div>
        </div>

        <div class="navigation">
            <a href="visualization_index.html" class="nav-button">返回主页</a>
            <a href="path_017.html" class="nav-button">上一个</a>
            <a href="path_019.html" class="nav-button">下一个</a>
        </div>
    </div>

    <script>
        // 网格数据
        const noFlyZones = [53, 63, 73];
        const patrolPath = [91, 81, 71, 61, 51, 41, 31, 21, 11, 12, 22, 32, 42, 52, 62, 72, 82, 92, 93, 83, 84, 74, 64, 54, 44, 43, 33, 23, 13, 14, 24, 34, 35, 25, 15, 16, 26, 36, 46, 45, 55, 65, 75, 85, 95, 94, 96, 86, 76, 66, 56, 57, 47, 37, 27, 17, 67, 77, 87, 97];
        const returnPath = [97, 96, 95, 94, 93, 92, 91];
        
        // 生成网格
        function generateGrid() {
            const grid = document.getElementById('pathGrid');
            
            // 创建7行9列的网格 (B7到B1, A1到A9)
            for (let row = 6; row >= 0; row--) {
                for (let col = 0; col < 9; col++) {
                    const positionCode = (col + 1) * 10 + (row + 1);
                    const cell = document.createElement('div');
                    cell.className = 'cell';
                    cell.textContent = positionCode;
                    
                    // 确定单元格类型
                    if (positionCode === 91) {
                        cell.classList.add('start');
                    } else if (noFlyZones.includes(positionCode)) {
                        cell.classList.add('no-fly');
                    } else if (patrolPath.includes(positionCode)) {
                        cell.classList.add('patrol');
                        // 添加路径序号
                        const pathIndex = patrolPath.indexOf(positionCode) + 1;
                        const pathNumber = document.createElement('div');
                        pathNumber.className = 'path-number';
                        pathNumber.textContent = pathIndex;
                        cell.appendChild(pathNumber);
                    } else if (returnPath.includes(positionCode)) {
                        cell.classList.add('return');
                        // 添加返航序号
                        const returnIndex = returnPath.indexOf(positionCode) + 1;
                        const pathNumber = document.createElement('div');
                        pathNumber.className = 'path-number';
                        pathNumber.textContent = 'R' + returnIndex;
                        cell.appendChild(pathNumber);
                    } else {
                        cell.classList.add('normal');
                    }
                    
                    grid.appendChild(cell);
                }
            }
        }
        
        // 页面加载时生成网格
        document.addEventListener('DOMContentLoaded', generateGrid);
    </script>
</body>
</html>
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的安全约束测试
"""

def test_diagonal_safety_logic():
    """测试对角线安全逻辑"""
    print("🔍 测试对角线移动安全逻辑")
    print("=" * 50)
    
    # 模拟禁飞区 [12, 13, 14]
    no_fly_zones = {12, 13, 14}
    
    def position_code_to_grid(position_code):
        """position_code转网格坐标"""
        if position_code < 11 or position_code > 97:
            return (-1, -1)
        
        col_num = position_code // 10  # A列号 (1-9)
        row_num = position_code % 10   # B行号 (1-7)
        
        if col_num < 1 or col_num > 9 or row_num < 1 or row_num > 7:
            return (-1, -1)
        
        row = row_num - 1  # B1->0, B2->1, ..., B7->6
        col = col_num - 1  # A1->0, A2->1, ..., A9->8
        
        return (row, col)
    
    def grid_to_position_code(row, col):
        """网格坐标转position_code"""
        if row < 0 or row >= 7 or col < 0 or col >= 9:
            return -1
        
        col_num = col + 1  # 0->A1, 1->A2, ..., 8->A9
        row_num = row + 1  # 0->B1, 1->B2, ..., 6->B7
        
        return col_num * 10 + row_num
    
    def is_diagonal_move_safe(from_row, from_col, to_row, to_col):
        """检查对角线移动是否安全"""
        # 对角线移动会经过两个中间位置
        intermediate_pos1 = (from_row, to_col)
        intermediate_pos2 = (to_row, from_col)
        
        # 转换为position_code检查是否为禁飞区
        pos1_code = grid_to_position_code(intermediate_pos1[0], intermediate_pos1[1])
        pos2_code = grid_to_position_code(intermediate_pos2[0], intermediate_pos2[1])
        
        if pos1_code in no_fly_zones or pos2_code in no_fly_zones:
            return False
        
        return True
    
    # 测试11→22的移动
    from_pos = position_code_to_grid(11)  # (0, 0)
    to_pos = position_code_to_grid(22)    # (1, 1)
    
    print(f"测试移动: 11{from_pos} → 22{to_pos}")
    print(f"禁飞区: {sorted(no_fly_zones)}")
    
    # 检查中间位置
    intermediate1 = (from_pos[0], to_pos[1])  # (0, 1)
    intermediate2 = (to_pos[0], from_pos[1])  # (1, 0)
    
    pos1_code = grid_to_position_code(intermediate1[0], intermediate1[1])  # 12
    pos2_code = grid_to_position_code(intermediate2[0], intermediate2[1])  # 21
    
    print(f"中间位置1: {intermediate1} -> position_code {pos1_code}")
    print(f"中间位置2: {intermediate2} -> position_code {pos2_code}")
    
    is_safe = is_diagonal_move_safe(from_pos[0], from_pos[1], to_pos[0], to_pos[1])
    
    print(f"移动安全性: {'❌ 不安全' if not is_safe else '✅ 安全'}")
    
    if not is_safe:
        print("✅ 安全约束生效：正确识别了不安全的对角线移动")
        if pos1_code in no_fly_zones:
            print(f"   冲突原因: 中间位置{pos1_code}是禁飞区")
        if pos2_code in no_fly_zones:
            print(f"   冲突原因: 中间位置{pos2_code}是禁飞区")
    else:
        print("❌ 安全约束失效：未能识别不安全的移动")
    
    # 测试其他移动
    print(f"\n测试其他对角线移动:")
    
    test_cases = [
        (31, 42, "不经过禁飞区"),
        (41, 52, "不经过禁飞区"),
        (23, 34, "可能经过禁飞区"),
    ]
    
    for from_code, to_code, description in test_cases:
        from_grid = position_code_to_grid(from_code)
        to_grid = position_code_to_grid(to_code)
        
        if from_grid == (-1, -1) or to_grid == (-1, -1):
            continue
            
        is_safe = is_diagonal_move_safe(from_grid[0], from_grid[1], to_grid[0], to_grid[1])
        status = "✅ 安全" if is_safe else "❌ 不安全"
        print(f"  {from_code} → {to_code} ({description}): {status}")

def main():
    """主函数"""
    print("🚀 简化安全约束测试")
    print("=" * 70)
    print("版权信息：米醋电子工作室")
    print("创建日期：2025-07-31")
    print("作者：Alex (工程师)")
    print("=" * 70)
    
    test_diagonal_safety_logic()
    
    print(f"\n🎉 安全约束逻辑测试完成!")

if __name__ == "__main__":
    main()

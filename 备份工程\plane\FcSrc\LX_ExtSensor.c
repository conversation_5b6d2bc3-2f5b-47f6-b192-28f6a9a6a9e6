/*==========================================================================
 * 描述    ：凌霄飞控外置传感器处理
 * 更新时间：2020-02-06
 * 作者		 ：匿名科创-Jyoun
 * 官网    ：www.anotc.com
 * 淘宝    ：anotc.taobao.com
 * 技术Q群 ：190169595
 * 项目合作：18084888982，18061373080
============================================================================
 * 匿名科创团队感谢大家的支持，欢迎大家进群互相交流、讨论、学习。
 * 若您觉得匿名有不好的地方，欢迎您拍砖提意见。
 * 若您觉得匿名好，请多多帮我们推荐，支持我们。
 * 匿名开源程序代码欢迎您的引用、延伸和拓展，不过在希望您在使用时能注明出处。
 * 君子坦荡荡，小人常戚戚，匿名坚决不会请水军、请喷子，也从未有过抹黑同行的行为。
 * 开源不易，生活更不容易，希望大家互相尊重、互帮互助，共同进步。
 * 只有您的支持，匿名才能做得更好。
===========================================================================*/
#include "LX_ExtSensor.h"
#include "Drv_AnoOf.h"
#include "DataTransfer.h"
#include "mid360.h"        // MID360激光雷达数据源
// #include "tofsense-m.h"    // 原TOF传感器数据源 - 已替换为TFmini
#include "tofmini.h"       // TFmini单点激光传感器数据源

_fc_ext_sensor_st ext_sens;

//这里把MID360激光雷达速度数据打包成通用速度传感器数据
static inline void General_Velocity_Data_Handle()
{
    // 检查MID360数据有效性 - 使用新的状态检查机制
    if (mid360_is_data_valid())
    {
			// MID360数据有效 - 直接使用速度数据
			ext_sens.gen_vel.st_data.hca_velocity_cmps[0] = mid360.speed_x_cms;  // X轴速度
			ext_sens.gen_vel.st_data.hca_velocity_cmps[1] = mid360.speed_y_cms;  // Y轴速度
			// Z轴速度始终设置为无效（不使用Z轴速度数据）
			ext_sens.gen_vel.st_data.hca_velocity_cmps[2] = 0x8000;
			// 触发速度数据发送
			AnoDTLxFrameSendTrigger(0x33);
    }
    else
    {
        // MID360数据无效 - 设置无效标记
        ext_sens.gen_vel.st_data.hca_velocity_cmps[0] = 0x8000;
        ext_sens.gen_vel.st_data.hca_velocity_cmps[1] = 0x8000;
    }


}

static inline void General_Distance_Data_Handle()
{
    // 检查TFmini传感器数据有效性 - 简化接口，无需sensor_id参数
    if (tofmini_is_distance_valid())
    {
        // TFmini传感器数据有效 - 使用简化的距离获取接口
        ext_sens.gen_dis.st_data.direction = 0;
        ext_sens.gen_dis.st_data.angle_100 = 270;
        ext_sens.gen_dis.st_data.distance_cm = (uint32_t)tofmini_get_distance_cm();  // uint16_t转uint32_t，移除sensor_id参数

        // 触发距离数据发送
        AnoDTLxFrameSendTrigger(0x34);
    }
    // 注意：如果TFmini数据无效，不发送距离数据包，保持原有逻辑
}

void LX_FC_EXT_Sensor_Task(float dT_s) //1ms
{
    //
    General_Velocity_Data_Handle();
    //
    General_Distance_Data_Handle();
}
